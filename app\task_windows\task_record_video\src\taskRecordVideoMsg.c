/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskRecordVideoWin.c"




static int videoKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
		{
			uiOpenWindow(&dateTimeWindow,0,0);
		}
	}
	return 0;
}



static int videoKeyMsgFormat(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiOpenWindow(&formatWindow,0,0);

	}
	return 0;
}





static int videoKeyMsgLedAdd(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_LedPwm_ctrl_Add();
		videoLedShow(handle);
	}
	return 0;
}

static int videoKeyMsgLedDec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_LedPwm_ctrl_Dec();
		videoLedShow(handle);
	}
	return 0;
}


static int videoKeyMsgPhotoRec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
		{	

			app_taskStart(TASK_RECORD_PHOTO,0);
		}
				

	}
	return 0;
}


/*******************************************************************************
* Function Name  : videoKeyMsgOk
* Description    : videoKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)
		{

			app_taskRecordVideo_stop();
			app_taskStart(TASK_RECORD_PHOTO,0);
			
		}/*else
		{

			if(1)//(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
			{
				SysCtrl.start_rec_or_takephoto_enable = 2;
				app_taskStart(TASK_RECORD_PHOTO,0);
			}else
				task_com_tips_insert_sd_start();
		}*/
			//app_taskRecordVideo_start();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgUp
* Description    : videoKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
	#if FUN_VIDEO_SHOW_ROTATE180_EN
		app_lcdVideoShowRotate180();
	#else
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
		{
			if(SysCtrl.lcdshow_win_mode == 0)
                SysCtrl.lcdshow_win_mode = LCDSHOW_WINBMAX;
			SysCtrl.lcdshow_win_mode--;
            app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
		}else
		{
		#if (1 == LCDSHOW_CSI_SCALE)
			if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINA)
			{
				while(hal_lcdWinUpdataCheckDone() < 0) hal_wdtClear();
				app_lcdVideoShowScaler_cfg(-1);
				videoscalerShow(handle);
			}
		#endif
		}
	#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgDown
* Description    : videoKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
	#if (CURRENT_CHIP == FPGA)
		husb_api_usensor_asternset(1);
	#elif (0 == LCDSHOW_CSI_SCALE)
		if(user_config_get(CONFIG_ID_AUDIOREC) == R_ID_STR_COM_ON)
		{
			user_config_set(CONFIG_ID_AUDIOREC,R_ID_STR_COM_OFF);
			videoRecordCmdSet(CMD_COM_AUDIOEN,0);
			hal_adc_volume_set(0);
			hal_adc_volume_setB(0);
		}
        else
        {
			user_config_set(CONFIG_ID_AUDIOREC,R_ID_STR_COM_ON);
			videoRecordCmdSet(CMD_COM_AUDIOEN,1);
			hal_adc_volume_set(100);
			hal_adc_volume_setB(100);
        }
		videoMicShow(handle);
	#elif 0
		hal_csi_save_raw_start("RAW000.raw");
		if(hal_csi_save_raw_state() == SAVE_RAW_START)
		{
			while(hal_csi_save_raw_state() == SAVE_RAW_START)
			{
				hal_wdtClear();
			}
			deg_Printf("CSI SAVE:%d\n",hal_csi_save_raw_state());
		}
	#else 
		//== lcd scale==
		if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINA)
		{
			while(hal_lcdWinUpdataCheckDone() < 0) hal_wdtClear();
			app_lcdVideoShowScaler_cfg(1);
			videoscalerShow(handle);
		}

	#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgMenu
* Description    : videoKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
		{
			uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
		}
		else
		{
			app_taskRecordVideo_Capture(CMD_PHOTO_RECORD_START);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgMode
* Description    : videoKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if(videoRecordGetStatus() != MEDIA_STAT_START)
			//app_taskChange();
			app_taskStart(TASK_RECORD_PHOTO,0);
		//else  // lock this file
		{
			/*if(SysCtrl.dev_stat_gsensorlock)
				SysCtrl.dev_stat_gsensorlock = 0;
			else
				SysCtrl.dev_stat_gsensorlock = 1;
			videoLockShow(handle);*/
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgSD
* Description    : videoSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	u32 tips_id;
	if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && videoRecordGetStatus() == MEDIA_STAT_START) // sdc out when recording
	{
		app_taskRecordVideo_stop();
	}
	// videoRemainTimeShow(handle);
	videoSDShow(handle);
	if(SysCtrl.dev_stat_sdc ==SDC_STAT_ERROR )
	{
		uiOpenWindow(&formatWindow,0,0);
		return 0;
	}

	switch(SysCtrl.dev_stat_sdc)
	{
		case SDC_STAT_NULL:  tips_id = TIPS_SD_NOT_INSERT; break;
		case SDC_STAT_FULL:  tips_id = TIPS_SD_FULL;       break;
		case SDC_STAT_ERROR: tips_id = TIPS_SD_ERROR;	   break;
		default: return 0;
	}
	uiOpenWindow(&tips1Window,0,2,tips_id, 2);

	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgUSB
* Description    : videoSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	videoBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgBattery
* Description    : videoSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		videoBaterryShow(handle);
	//if(SysCtrl.dev_stat_battery <= BATTERY_STAT_2)
	//{
	//	if(husb_api_usensor_tran_sta())
	//	{
	//		app_taskRecordVideo_stop();
	//		videoRemainTimeShow(handle);
	//	}
	//}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgMD
* Description    : videoSysMsgMD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgMD(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.rec_md_time == 0 && videoRecordGetStatus() != MEDIA_STAT_START)
	{
		videoRecordCmdSet(CMD_COM_MDTIME, FUN_MOTION_DEC_TIME*1000);
		app_taskRecordVideo_start();
		SysCtrl.rec_md_time	= 1;//XOSTimeGet();
		deg_Printf("md start\n");
	}

	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgRecord
* Description    : videoSysMsgRecord
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgRecord(winHandle handle,u32 parameNum,u32* parame)
{
	u32 type = MSG_RECORD_MAX;
	if(parameNum == 1)
		type = parame[0];
	if(type == MSG_RECORD_MAX)
	{
		return 0;
	}
	else if(type == MSG_RECORD_START)
	{
		SysCtrl.rec_show_time = 0;
		//SysCtrl.dev_stat_gsensorlock = 0;
		videoRecTimeShow(handle);
		// videoRedDotBlinkShow(handle, 1);
	}else if(type == MSG_RECORD_STOP || type == MSG_RECORD_ERROR)
	{
		SysCtrl.dev_stat_gsensorlock = 0;
		// videoRemainTimeShow(handle);
		// videoRedDotBlinkShow(handle, 0);
	}else if(type == MSG_RECORD_RESTART)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
			app_taskRecordVideo_start();
	}else if(type == MSG_RECORD_LOCK)
	{
		SysCtrl.dev_stat_gsensorlock = 1;
	}
	videoLockShow(handle);
	if(type == MSG_RECORD_START)
		app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsg1S
* Description    : videoSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	static u32 flag = 0;
	/*if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST)
	{
	    if(!(SysCtrl.dev_stat_power & POWERON_FLAG_WAIT))
        {
            if(SysCtrl.dev_dusb_stat < USBDEV_STAT_DEVIN && SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL && SysCtrl.dev_stat_battery > BATTERY_STAT_2)
            {
                deg_Printf("power on,start record\n");
                XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_RESTART));
            }
            SysCtrl.dev_stat_power &= ~POWERON_FLAG_FIRST;
        }


	}*/
	if(SysCtrl.start_rec_or_takephoto_enable == 1)
	{
		SysCtrl.start_rec_or_takephoto_enable = 0;


		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_RESTART));
		
	}
	videoSysTimeShow(handle);
	videoPoweOnTimeShow(handle);
	videoIrLedShow(handle);
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		if(uiWinIsVisible(winItem(handle,VIDEO_BATERRY_ID)))
			uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),0);
		else
		{
            uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),1);
			uiWinSetResid(winItem(handle,VIDEO_BATERRY_ID),R_ID_ICON_MTBATTERY5);
		}
	}
	//if(user_config_get(CONFIG_ID_MOTIONDECTION) == R_ID_STR_COM_ON)
	//{
	//	if(SysCtrl.rec_md_time && XOSTimeGet()- SysCtrl.rec_md_time >= FUN_MOTION_DEC_TIME*1000)
	//	{
	//		app_taskRecordVideo_stop();
	//		SysCtrl.rec_md_time = 0;
	//	}
	//}
	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		if(flag&1)
			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
		else
			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
		flag ^= 1;
	}
	//app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgTimeUpdate
* Description    : videoSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		//videoRecTimeShow(handle);
		videoRedDotBlinkShow(handle, 1);
	}else
	{
		// videoRemainTimeShow(handle);
		videoRedDotBlinkShow(handle, 0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoOpenWin
* Description    : videoOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoOpenWin\n");

	uiWinSetResid(winItem(handle,VIDEO_MODE_ID),R_ID_ICON_MTRECORD);
	//videoRemainTimeShow(handle);
	videoPoweOnTimeShow(handle);
	videoResolutionShow(handle);
	videoMDShow(handle);
	videoMonitorShow(handle);
	videoIrLedShow(handle);
	videoLockShow(handle);
	videoSDShow(handle);
	videoMicShow(handle);
	videoBaterryShow(handle);
	videoSysTimeShow(handle);

	videoscalerShow(handle);
	videoLedShow(handle);



/*	if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST)
	{
		if(SysCtrl.dev_dusb_stat != USBDEV_STAT_PC && SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL && SysCtrl.dev_stat_battery > BATTERY_STAT_2)
		{
			deg_Printf("power on,start record\n");
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_RESTART));
		}
		SysCtrl.dev_stat_power &= ~POWERON_FLAG_FIRST;

	}*/

	return 0;
}
/*******************************************************************************
* Function Name  : videoCloseWin
* Description    : videoCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : videoWinChildClose
* Description    : videoWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoWinChildClose\n");
	if(videoRecordGetStatus() != MEDIA_STAT_START)
		videoRemainTimeShow(handle);
	videoPoweOnTimeShow(handle);
	videoResolutionShow(handle);
	videoMDShow(handle);
	videoMonitorShow(handle);
	videoIrLedShow(handle);
	videoLockShow(handle);
	videoSDShow(handle);
	videoMicShow(handle);
	videoBaterryShow(handle);
	videoRedDotBlinkShow(handle, 0);

	return 0;
}
/*******************************************************************************
* Function Name  : videoWinChildOpen
* Description    : videoWinChildOpen
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoWinChildOpen(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]videoWinChildOpen\n");
	return 0;
}
/*******************************************************************************
* Function Name  : videoTouchWin
* Description    : videoTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoTouchWin(winHandle handle,uint32 parameNum,uint32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	if(parameNum!=3)
	{
		deg_Printf("videoRecordTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
//		if(parame[0]== VIDEO_MIC_ID)
//		{
//			if(user_config_get(CONFIG_ID_AUDIOREC) == R_ID_STR_COM_ON)
//			{
//				user_config_set(CONFIG_ID_AUDIOREC,R_ID_STR_COM_OFF);
//				videoRecordCmdSet(CMD_COM_AUDIOEN,0);
//				hal_adc_volume_set(0);
//				hal_adc_volume_setB(0);
//			}
//       	else
//       	{
//				user_config_set(CONFIG_ID_AUDIOREC,R_ID_STR_COM_ON);
//				videoRecordCmdSet(CMD_COM_AUDIOEN,1);
//				hal_adc_volume_set(100);
//				hal_adc_volume_setB(100);
//       	}
//			videoMicShow(handle);
//		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSlideOff
* Description    : videoSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSlideOff(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(parameNum!=1)
	{
		deg_Printf("videoRecordSlidRelease, parame num error %d\n",parameNum);
		return 0;
	}
	//if(parame[0] == TP_DIR_UP)
	//	XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_DOWN,KEY_PRESSED));
	//else 
	if(parame[0] == TP_DIR_DOWN)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MENU,KEY_PRESSED));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	return 0;
}
static int recordvideoKeyMsgChangeLed(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		// task_com_LedOnOffChange();

	}
	return 0;
}

ALIGNED(4) msgDealInfor recordVideoMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		videoOpenWin},
	{SYS_CLOSE_WINDOW,		videoCloseWin},
	{SYS_CHILE_COLSE,		videoWinChildClose},
	{SYS_CHILE_OPEN,		videoWinChildOpen},
	{SYS_TOUCH_WINDOW,      videoTouchWin},
	{SYS_TOUCH_SLIDE_OFF,   videoSlideOff},

	//{KEY_EVENT_OK,			videoKeyMsgPhotoRec	},
	{KEY_EVENT_OK,			recordvideoKeyMsgChangeLed},


	{KEY_EVENT_DOWN,			videoKeyMsgDown},
	{KEY_EVENT_UP,		videoKeyMsgUp},
	//{KEY_EVENT_MENU,		videoKeyMsgMenu},
	{KEY_EVENT_MODE,		videoKeyMsgMode},

	{KEY_EVENT_LED_DEC,		videoKeyMsgOk},
	{KEY_EVENT_PHOTO_REC,	videoKeyMsgPhotoRec},

	{KEY_EVENT_POWER,		videoKeyMsgLedAdd},


//	{KEY_EVENT_LED_DEC,		videoKeyMsgLedDec},
	//{KEY_EVENT_DEL, 		videoKeyMsgFormat},
//	{KEY_EVENT_POWER,		videoKeyMsgPower},

	
	{SYS_EVENT_SDC,			videoSysMsgSD},
	{SYS_EVENT_USBDEV,		videoSysMsgUSB},
	{SYS_EVENT_BAT,			videoSysMsgBattery},
	{SYS_EVENT_MD,			videoSysMsgMD},
	{SYS_EVENT_RECORD,		videoSysMsgRecord},
	{SYS_EVENT_1S,			videoSysMsg1S},
	{SYS_EVENT_TIME_UPDATE, videoSysMsgTimeUpdate},
	{EVENT_MAX,				NULL},
};

WINDOW(recordVideoWindow,recordVideoMsgDeal,recordVideoWin)


