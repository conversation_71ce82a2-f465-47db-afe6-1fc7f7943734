/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  USER_CONFIG_API_H
    #define  USER_CONFIG_API_H
	
#include "user_config_typedef.h"

//用户需要根据自己的板子的硬件接口来include对应的 “user_hardware_cfg_xxx.h” 文件
#if CURRENT_CHIP == FPGA
	#include "user_hardware_cfg_fpga.h"
#else
	//#include "user_hardware_cfg_hx3302B_demo.h"
	//#include "user_hardware_cfg_hx3302B_X200.h"
	#include "user_hardware_cfg_hx3302B_CG01.h"
#endif

//---------file dir CFG--------------------------------------------------------------------------------------------------
#define  FILEDIR_RECA 			    "RECA/"
#define  FILEDIR_RECB 			    "RECB/"
#define  FILEDIR_REC 				"REC/"
#define  FILEDIR_IMG  			    "IMG/"
#define  FILEDIR_WAV  			    "WAV/"
#define  FILEDIR_MP3  				"MP3/" 
#define  FILEDIR_NES  				"GAME/"

#if LCD_TAG_SELECT == LCD_MCU_3030B
	#define LCD_VER						"3030"
#elif LCD_TAG_SELECT == LCD_MCU_ST7789V
	#define LCD_VER						"7789"		
#elif LCD_TAG_SELECT == LCD_MCU_JD9853
	#define LCD_VER						"9853"
#elif LCD_TAG_SELECT == LCD_MCU_GC9307
 	#define LCD_VER						"GC9307"
#endif


#if NV_JPG_MAX_NUM == 200
#define JPG_NUM "200"
#elif (NV_JPG_MAX_NUM == 400)
#define JPG_NUM "400"
#endif
//--------------RESOURCE LOAD CFG-----------------------------------------------------------------------------------------
#define  VERSION_LOAD_AUTO     		0  				  	//定义软件版本是否自动从资源获取
#define  SYSTEM_VERSION     	 	LCD_VER"_250731"  	//当定义 VERSION_LOAD_AUTO为0时，软件版本由此宏定义

#define  KEYSOUND_LOAD_AUTO    		1  						// 定义是否从资源load按键音

#define  DATETIME_LOAD_AUTO    		1  						// 定义是否从资源获取默认的系统时间


//----------------------SYS FUNCTION CFG----------------------------------------------------------------------------------
//-------lcd  show cfg-----
#define  UI_SHOW_SMALL_PANEL		0//当屏幕尺寸小于320*240时，去除MENU显示右侧子项
 //1: 支持LCD屏显画面放大缩小; 0: 不支持LCD缩放
#define  LCDSHOW_CSI_SCALE      	1
 //1: 支持回放放大缩小	0：不支持回放放大缩小
#define  LCDSHOW_PLAY_SCALE      	0

//可配置在原有屏的基础上VIDEO层旋转：
//LCD_DISPLAY_ROTATE_NONE / LCD_DISPLAY_ROTATE_0 ：保持不变
//LCD_DISPLAY_ROTATE_90 ：逆时针旋转90度
//LCD_DISPLAY_ROTATE_180 ：逆时针旋转180度
//LCD_DISPLAY_ROTATE_270 ：逆时针旋转270度
//LCD_DISPLAY_MIRROR_NONE : 保持不变
//LCD_DISPLAY_V_MIRROR ： 水平镜像
//LCD_DISPLAY_H_MIRROR ：垂直镜像
#define  LCDSHOW_VIDEO_ROTATE_MORE_MODE		(LCD_DISPLAY_ROTATE_NONE |LCD_DISPLAY_MIRROR_NONE) 

//可配置在原有屏的基础上UI层旋转：(注意客户需要自行考虑UI)
//LCD_DISPLAY_ROTATE_NONE / LCD_DISPLAY_ROTATE_0 ：保持不变
//LCD_DISPLAY_ROTATE_90 ：逆时针旋转90度
//LCD_DISPLAY_ROTATE_180 ：逆时针旋转180度
//LCD_DISPLAY_ROTATE_270 ：逆时针旋转270度
#define  LCDSHOW_UI_ROTATE_MORE_MODE		(LCD_DISPLAY_ROTATE_270) 

//配置缩放比例，配置为0是表示最大比例缩放，即填满全屏 (注意客户需要自行考虑UI)
#define  LCDSHOW_RATIO_CFG					LCD_RATIO_MAKE(0, 0) 

//配置为1 ： 裁剪sensor以达到比例缩放
#define  LCDSHOW_RATIO_BY_SENSOR			LCD_RATIO_MAKE(4, 3) 	

//-------FUN CFG------------
#define  FUN_AUTOPOWEROFF_TIME				0 // 定义电池供电情况下，自动关机时间，单位：秒

#define  FUN_MOTION_DEC_TIME    			20 // 定义动态侦测启动录像时，录像时间长度，单位：秒

#define  FUN_KEYSOUND_VOLUME    			100 // 定义按键音量，范围： [0-100]

#define  FUN_AUDIO_RECORD_EN       			0	//1: 打开录音和播放录音任务

#define  FUN_VIDEO_SYNC_WRITE				0   //1: 定义录像文件定时回写，用于录像过程意外掉电时，录像文件仍旧能正常播放

#define  FUN_MP3_PLAY_EN       		        (0 & MP3_EN)	//1: Enable MP3 play function

#define  DEV_SENSOR_FILTER_EN				0 //1: enable sensor change between color and nocolr, press power key to function(for example)

#define  FUN_VIDEO_PLAY_SPEED				0   //1: 支持文件快进或快退播放		

#define  FUN_VIDEO_SHOW_ROTATE180_EN		0  //1:支持video层显示动态旋转180度


//-------------DEV CFG(battery, gsensor,ir,key,lcd,led,sd,sensor,usb...)---------------------------------------------------
//-------dev enable and dev io cfg, please check and modify file"dev/dev_api.h"




#define  DEV_SDC_TIMEOUT					2000//定义SD卡读写出错超时时间，单位ms。在录像过程中，拔卡响应时间，建议配置1000ms


#define  TASK_SCAN_FILE_EVERY_TIME			0

#define  FUN_BATTERY_CHARGE_SHOW			1  //1: 电池充电时显示界面


#define	ENCRYPT_FUNC_SWITCH					1	//加密功能开关 0：关 		1：开


#define ADD_FORMAT_MENU_EN					0//短按POWER键能弹出时间设置菜单 显示要时间 拍照 录像水印'不'保留

#define ADD_FORMAT1_MENU_EN					0//长按回放键能弹出时间设置菜单 显示'不'要时间 拍照 录像水印保留

#define FUNC_AF_ENABLE						1//AF自动对焦使能	0:关闭	1：开启


#endif




