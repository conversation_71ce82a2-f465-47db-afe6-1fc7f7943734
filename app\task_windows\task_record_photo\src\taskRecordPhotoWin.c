/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	PHOTO_MODE_ID =0,
	PHOTO_RESOLUTION_ID,

	PHOTO_POWERON_TIME_ID,
	PHOTO_BATERRY_ID,

	PHOTO_SYSTIME_ID,

	PHOTO_IRLED_ID,
	PHOTO_MD_ID,// motion detect
	PHOTO_MONITOR_ID, // parking monitoring

	PHOTO_SD_ID,
	PHOTO_MIC_ID,
	
	PHOTO_LED_ID,
	PHOTO_LED_LEVEL_ID,
	PHOTO_SCALER_ID,
	PHOTO_TAKE_SUCCESS_ID,
	PHOTO_NO_INSERT_SD_ID,


	PHOTO_TEST_AF_ID,


	PHOTO_AF_LINE_00,
	PHOTO_AF_LINE_01,
	PHOTO_AF_LINE_10,
	PHOTO_AF_LINE_11,
	PHOTO_AF_LINE_20,
	PHOTO_AF_LINE_21,
	PHOTO_AF_LINE_30,
	PHOTO_AF_LINE_31,

	PHOTO_MAX_ID
};
/*******************************************************************************
* Function Name  : recordPhotoWin
* Description    : recordPhotoWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED ALIGNED(4) const widgetCreateInfor recordPhotoWin[] =
{
	createFrameWin(							Rx(0),	 Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createImageIcon(PHOTO_MODE_ID,          Rx(0), 	 Ry(0),   Rw(34),  Rh(32), R_ID_ICON_MTPHOTO,ALIGNMENT_LEFT),
	//createStringIcon(PHOTO_RESOLUTION_ID,	Rx(105), Ry(0),   Rw(40),  Rh(25), RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_White,	DEFAULT_FONT),

	//createStringIcon(PHOTO_POWERON_TIME_ID,	Rx(250), Ry(0),   Rw(45),  Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createImageIcon(PHOTO_BATERRY_ID,    	Rx(290), Ry(0),   Rw(45),  Rh(32), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),

	createStringIcon(PHOTO_SYSTIME_ID,      Rx(10),   Ry(215), Rw(220), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),

	//createImageIcon(PHOTO_IRLED_ID,     	Rx(270), Ry(165), Rw(25),  Rh(25), 	R_ID_ICON_MTIROFF,		ALIGNMENT_CENTER),

	//createImageIcon(PHOTO_MD_ID,        	Rx(270), Ry(190), Rw(25),  Rh(25), 	R_ID_ICON_MTMOTION,		ALIGNMENT_CENTER),
	//createImageIcon(PHOTO_MONITOR_ID,   	Rx(270), Ry(215), Rw(25),  Rh(25), 	R_ID_ICON_MTPARKOFF,	ALIGNMENT_CENTER),

	createImageIcon(PHOTO_SD_ID,        	Rx(250), Ry(0), Rw(32),  Rh(32), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	//createImageIcon(PHOTO_MIC_ID,       	Rx(295), Ry(215), Rw(25),  Rh(25), R_ID_ICON_MTMICOFF,		ALIGNMENT_CENTER),

	createStringIcon(PHOTO_SCALER_ID,		Rx(270), Ry(215), Rw(45),  Rh(35),	RAM_ID_MAKE(" "),	ALIGNMENT_CENTER,	R_ID_PALETTE_White,	DEFAULT_FONT),

	createImageIcon(PHOTO_TAKE_SUCCESS_ID,	Rx(120), Ry(105), Rw(80),  Rh(30),	R_ID_ICON_CG_PHOTO_SUCCESS,	ALIGNMENT_CENTER),

		

	// createImageIcon(PHOTO_LED_ID,			Rx(237), Ry(0), Rw(30),  Rh(30),	R_ID_ICON_CG_LED3,	ALIGNMENT_CENTER),



	
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : recordPhotoResShow
* Description    : recordPhotoResShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static  void recordPhotoResShow(winHandle handle)
{
	switch(user_config_get(CONFIG_ID_PRESLUTION))
	{
		case R_ID_STR_RES_1M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("1M")); break;
		case R_ID_STR_RES_2M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("2M")); break;
		case R_ID_STR_RES_3M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("3M")); break;
		case R_ID_STR_RES_5M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("5M")); break;
		case R_ID_STR_RES_8M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("8M")); break;
		case R_ID_STR_RES_10M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("10M")); break;
		case R_ID_STR_RES_12M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("12M")); break;
		default:  			  uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("???")); break;
	}
}
/*******************************************************************************
* Function Name  : recordPhotoPowerOnTimeShow
* Description    : recordPhotoPowerOnTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoPowerOnTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PHOTO_POWERON_TIME_ID),RAM_ID_MAKE(task_com_powerOnTime_str()));
}
/*******************************************************************************
* Function Name  : recordPhotoBatteryShow
* Description    : recordPhotoBatteryShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoBatteryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PHOTO_BATERRY_ID),batid);

}
/*******************************************************************************
* Function Name  : videoSysTimeShow
* Description    : videoSysTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoSysTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PHOTO_SYSTIME_ID),RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())));
}
/*******************************************************************************
* Function Name  : recordPhotoIrLEDShow
* Description    : recordPhotoIrLEDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoIrLEDShow(winHandle handle)
{
	if(hardware_setup.ir_led_en)
	{
		if(user_config_get(CONFIG_ID_IR_LED)==R_ID_STR_COM_OFF)
			uiWinSetResid(winItem(handle,PHOTO_IRLED_ID),R_ID_ICON_MTIROFF);
		else
			uiWinSetResid(winItem(handle,PHOTO_IRLED_ID),R_ID_ICON_MTIRON);
	}else
	{
		uiWinSetVisible(winItem(handle,PHOTO_IRLED_ID),0);
	}
}
/*******************************************************************************
* Function Name  : recordPhotoMDShow
* Description    : recordPhotoMDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoMDShow(winHandle handle)
{

	if(user_config_get(CONFIG_ID_MOTIONDECTION)==R_ID_STR_COM_ON)
	{
	    uiWinSetVisible(winItem(handle,PHOTO_MD_ID),1);
		uiWinSetResid(winItem(handle,PHOTO_MD_ID),R_ID_ICON_MTMOTION);

	}
	else
		uiWinSetVisible(winItem(handle,PHOTO_MD_ID),0);
}
/*******************************************************************************
* Function Name  : recordPhotoMonitorShow
* Description    : recordPhotoMonitorShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoMonitorShow(winHandle handle)
{

	if(user_config_get(CONFIG_ID_PARKMODE)==R_ID_STR_COM_ON)
		uiWinSetResid(winItem(handle,PHOTO_MONITOR_ID),R_ID_ICON_MTPARKON);
	else
		uiWinSetResid(winItem(handle,PHOTO_MONITOR_ID),R_ID_ICON_MTPARKOFF);
}

/*******************************************************************************
* Function Name  : recordPhotoSDShow
* Description    : recordPhotoSDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : recordPhotoMicShow
* Description    : recordPhotoMicShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoMicShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_AUDIOREC)==R_ID_STR_COM_OFF)
		uiWinSetResid(winItem(handle,PHOTO_MIC_ID),R_ID_ICON_MTMICOFF);
	else
		uiWinSetResid(winItem(handle,PHOTO_MIC_ID),R_ID_ICON_MTMICON);
}


UNUSED static void recordPhotoLedShow(winHandle handle)
{
	resID ledid;

	switch(SysCtrl.led_pwm_level)
	{
		case 0: ledid = R_ID_ICON_CG_LED0; break;
		case 1: ledid = R_ID_ICON_CG_LED1; break;
		case 2: ledid = R_ID_ICON_CG_LED2; break;
		case 3: ledid = R_ID_ICON_CG_LED3; break;
		default:
									 break;
	}
	uiWinSetResid(winItem(handle,PHOTO_LED_ID),ledid);

	


}

UNUSED static void recordPhotoscalerShow(winHandle handle)
{
	resID scalerid;
	switch(SysCtrl.lcd_scaler_level_uishow)
	{
		case 0: uiWinSetResid(winItem(handle,PHOTO_SCALER_ID),RAM_ID_MAKE("1.0X")); break;
		case 1: uiWinSetResid(winItem(handle,PHOTO_SCALER_ID),RAM_ID_MAKE("2.0X")); break;
		case 2: uiWinSetResid(winItem(handle,PHOTO_SCALER_ID),RAM_ID_MAKE("3.0X")); break;
		case 3: uiWinSetResid(winItem(handle,PHOTO_SCALER_ID),RAM_ID_MAKE("4.0X")); break;
		default:
									 break;
	}



}

UNUSED static void recordPhotoTakePhotoSuccessShow(winHandle handle,u8 enable)
{

	if(enable)
	{
	    uiWinSetVisible(winItem(handle,PHOTO_TAKE_SUCCESS_ID),1);
		uiWinSetResid(winItem(handle,PHOTO_TAKE_SUCCESS_ID),R_ID_ICON_CG_PHOTO_SUCCESS);

	}
	else
		uiWinSetVisible(winItem(handle,PHOTO_TAKE_SUCCESS_ID),0);
}





UNUSED static void recordPhotoAF_test_Show(winHandle handle)
{
	static char AFStr[]= "1023";	
	hx330x_num2str(AFStr, SysCtrl.af_value, 4);
	uiWinSetResid(winItem(handle,PHOTO_TEST_AF_ID),RAM_ID_MAKE(AFStr));
}


UNUSED static  void recordPhotoAFLineShow(winHandle handle, u32 sta)
{
	uiColor color;
	u32 visable;
	switch(sta)
	{
		case 0: color = R_ID_PALETTE_Transparent; visable = 0;break;
		case 1: color = R_ID_PALETTE_Red;		  visable = 1;break;
		case 2: color = R_ID_PALETTE_Yellow; visable = 1;break;
		default: return;
	}
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_00), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_01), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_10), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_11), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_20), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_21), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_30), color);
	uiWinSetbgColor(winItem(handle,PHOTO_AF_LINE_31), color);

	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_00),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_01),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_10),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_11),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_20),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_21),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_30),visable);
	uiWinSetVisible(winItem(handle,PHOTO_AF_LINE_31),visable);
}