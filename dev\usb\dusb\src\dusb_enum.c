/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

//#define _DEBG_USB_ENUM__

ALIGNED(4) const uint8_t SDK_CHIP_INF[4] = {0x00, 0x00, 0x01, 0x05}; 

ALIGNED(4) const u8 dusb_msc_devdsc[]= {
	0x12, DEVICE_DESCRIPTOR, bcdUSB20&0xff, (bcdUSB20>>8)&0xff,  0x00, 0x00, 0x00, EP0_MAX_SIZE,  DEV_VID&0xff, (DEV_VID>>8)&0xff, DEV_PID&0xff, (DEV_PID>>8)&0xff, bcdDEVICE&0xff, (bcdDEVICE>>8)&0xff, 0x01, 0x00, 0x00, 0x01, 
};
ALIGNED(4) const u8 dusb_com_devdsc[]={
	0x12, DEVICE_DESCRIPTOR, bcdUSB20&0xff, (bcdUSB20>>8)&0xff,  0xEF, 0x02, 0x01, EP0_MAX_SIZE,  DEV_VID&0xff, (DEV_VID>>8)&0xff, DEV_PID&0xff, (DEV_PID>>8)&0xff, bcdUVCDEVICE&0xff, (bcdUVCDEVICE>>8)&0xff, 0x01, 0x02, 0x03, 0x01,
};

//cfg描述符需要修改，去掉const
ALIGNED(4) u8 dusb_msc_cfgdsc[]=
{	
	#define _MSC_CFG_SIZE_		(0x20)
	0x09, CONFIGURATION_DESCRIPTOR, _MSC_CFG_SIZE_&0xff, _MSC_CFG_SIZE_>>8,  1, 0x01, 0x00, 0x80|((SELF_POWER^1)<<6)|(REMOTE_WAKEUP<<5), 0x32, 
	0x09, INTERFACE_DESCRIPTOR, DMSC_INTFS, 0x00, 0x02, CLASS_MASSSTORAGE, SUBCLASS_SCSI, PROTOCOL_BULKONLY, 0x00,
	0x07, ENDPOINT_DESCRIPTOR, DEV_TXEP_MASS|0x80, EP_TYPE_BULK, BULK_MAX_SIZE_HS&0xff, BULK_MAX_SIZE_HS>>8, 0x01,
	0x07, ENDPOINT_DESCRIPTOR, DEV_RXEP_MASS, EP_TYPE_BULK, BULK_MAX_SIZE_HS&0xff, BULK_MAX_SIZE_HS>>8, 0x01,
};
//cfg描述符需要修改，去掉const
ALIGNED(4) u8 dusb_com_cfgdsc[]=
{
	#define _COM_CFG_SIZE_		(0x09+0x17+0x15c+0x6b)
	0x09, CONFIGURATION_DESCRIPTOR, _COM_CFG_SIZE_&0xff, _COM_CFG_SIZE_>>8, 1+2+2, 0x01, 0x00, 0x80|((SELF_POWER^1)<<6)|(REMOTE_WAKEUP<<5), 0x80, 

//UVC_CONFIG_DESC size = 0x08+0x09+_VC_DESC_SIZE_+0x07+0x05+0x09+_VS_DESC_SIZE_+0x09+0x07 +0x09+0x07 = 0x15c
//{
	0x08, IAD_DESC, DUVC_CTL_INTFS, 0x02, CC_VIDEO, SC_VIDEO_INTERFACE_COLLECTION, 0x00, 0x02, //Index of string descriptor	"HD Camera v2.10"
	0x09, INTERFACE_DESCRIPTOR, DUVC_CTL_INTFS, 0x00, 0x01, CC_VIDEO, SC_VIDEOCONTROL, 0x00, 0x02, 
	#define  _VC_DESC_SIZE_		(0x0d+0x12+0x0b+0x09)  //0x33
	0x0D, CS_INTERFACE, VC_HEADER, bcdDEVICE&0xff, bcdDEVICE>>8, _VC_DESC_SIZE_&0xff, _VC_DESC_SIZE_>>8, UVC_FREQ_S&0xff, (UVC_FREQ_S>>8)&0xff, (UVC_FREQ_S>>16)&0xff, (UVC_FREQ_S>>24)&0xff, 0x01, 0x01, 
	0x12, CS_INTERFACE, VC_INPUT_TERMINAL, UVC_INPUT_ID, ITT_CAMERA&0xff, ITT_CAMERA>>8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 
	0x0B, CS_INTERFACE, VC_PROCESSING_UNIT, UVC_PROCESS_ID, UVC_INPUT_ID, 0x00, 0x00, 0x02, 0x7F, 0x17, 0x00, 
	0x09, CS_INTERFACE, VC_OUTPUT_TERMINAL, UVC_OUTPUT_ID, TT_STREAMING&0xff, TT_STREAMING>>8, 0x00, UVC_PROCESS_ID, 0x00, 
	 
	0x07, ENDPOINT_DESCRIPTOR, DEV_TXEP_KEY|0x80, EP_TYPE_INTERRUPT, 0x0a, 0x00, 0x04, 
	0x05, CS_ENDPOINT, EP_INTERRUPT, 0x40, 0x00, 
	
	0x09, INTERFACE_DESCRIPTOR, DUVC_STRM_INTFS, 0x00, 0x00, CC_VIDEO, SC_VIDEOSTREAMING, 0x00, 0x00, 
	#define _VS_DESC_SIZE_		(0x0e + 0x0b + 0x22*5 + 0x1a + 0x06) //0xe3
	0x0E, CS_INTERFACE, VS_INPUT_HEADER, 0x01, _VS_DESC_SIZE_&0xff, _VS_DESC_SIZE_>>8, DEV_TXEP_UVC|0x80, 0x00, UVC_OUTPUT_ID, 0x02, 0x01, 0x00, 0x01, 0x00, 
	0x0B, CS_INTERFACE, VS_FORMAT_MJPEG, 0x01, MAX_SOL, 0x01, DEFAULT_SOL, 0x00, 0x00, 0x00, 0x00, 
	//SOL1
	0x22, CS_INTERFACE, VS_FRAME_MJPEG, SOL1, 0x01, SOL1_W&0xff,SOL1_W>>8,SOL1_H&0xff,SOL1_H>>8, 
		SOL1_MIN_BITRATE&0xff,(SOL1_MIN_BITRATE>>8)&0xff,(SOL1_MIN_BITRATE>>16)&0xff,(SOL1_MIN_BITRATE>>24)&0xff, 
		SOL1_MAX_BITRATE&0xff,(SOL1_MAX_BITRATE>>8)&0xff,(SOL1_MAX_BITRATE>>16)&0xff,(SOL1_MAX_BITRATE>>24)&0xff, 
		SOL1_VIDEO_FRAME_SIZE&0xff,(SOL1_VIDEO_FRAME_SIZE>>8)&0xff,(SOL1_VIDEO_FRAME_SIZE>>16)&0xff,(SOL1_VIDEO_FRAME_SIZE>>24)&0xff,
		UVC_FPS_DEFAULT&0xff,(UVC_FPS_DEFAULT>>8)&0xff,(UVC_FPS_DEFAULT>>16)&0xff,(UVC_FPS_DEFAULT>>24)&0xff,
		0x02, 
		UVC_FPS_MAX&0xff,(UVC_FPS_MAX>>8)&0xff,(UVC_FPS_MAX>>16)&0xff,(UVC_FPS_MAX>>24)&0xff, 
		UVC_FPS_MIN&0xff,(UVC_FPS_MIN>>8)&0xff,(UVC_FPS_MIN>>16)&0xff,(UVC_FPS_MIN>>24)&0xff, 
	//SOL2
	0x22, CS_INTERFACE, VS_FRAME_MJPEG, SOL2, 0x01, SOL2_W&0xff,SOL2_W>>8,SOL2_H&0xff,SOL2_H>>8, 
		SOL2_MIN_BITRATE&0xff,(SOL2_MIN_BITRATE>>8)&0xff,(SOL2_MIN_BITRATE>>16)&0xff,(SOL2_MIN_BITRATE>>24)&0xff, 
		SOL2_MAX_BITRATE&0xff,(SOL2_MAX_BITRATE>>8)&0xff,(SOL2_MAX_BITRATE>>16)&0xff,(SOL2_MAX_BITRATE>>24)&0xff, 
		SOL2_VIDEO_FRAME_SIZE&0xff,(SOL2_VIDEO_FRAME_SIZE>>8)&0xff,(SOL2_VIDEO_FRAME_SIZE>>16)&0xff,(SOL2_VIDEO_FRAME_SIZE>>24)&0xff,
		UVC_FPS_DEFAULT&0xff,(UVC_FPS_DEFAULT>>8)&0xff,(UVC_FPS_DEFAULT>>16)&0xff,(UVC_FPS_DEFAULT>>24)&0xff,
		0x02, 
		UVC_FPS_MAX&0xff,(UVC_FPS_MAX>>8)&0xff,(UVC_FPS_MAX>>16)&0xff,(UVC_FPS_MAX>>24)&0xff, 
		UVC_FPS_MIN&0xff,(UVC_FPS_MIN>>8)&0xff,(UVC_FPS_MIN>>16)&0xff,(UVC_FPS_MIN>>24)&0xff,
	//SOL3
	0x22, CS_INTERFACE, VS_FRAME_MJPEG, SOL3, 0x01, SOL3_W&0xff,SOL3_W>>8,SOL3_H&0xff,SOL3_H>>8, 
		SOL3_MIN_BITRATE&0xff,(SOL3_MIN_BITRATE>>8)&0xff,(SOL3_MIN_BITRATE>>16)&0xff,(SOL3_MIN_BITRATE>>24)&0xff, 
		SOL3_MAX_BITRATE&0xff,(SOL3_MAX_BITRATE>>8)&0xff,(SOL3_MAX_BITRATE>>16)&0xff,(SOL3_MAX_BITRATE>>24)&0xff, 
		SOL3_VIDEO_FRAME_SIZE&0xff,(SOL3_VIDEO_FRAME_SIZE>>8)&0xff,(SOL3_VIDEO_FRAME_SIZE>>16)&0xff,(SOL3_VIDEO_FRAME_SIZE>>24)&0xff,
		UVC_FPS_DEFAULT&0xff,(UVC_FPS_DEFAULT>>8)&0xff,(UVC_FPS_DEFAULT>>16)&0xff,(UVC_FPS_DEFAULT>>24)&0xff,
		0x02, 
		UVC_FPS_MAX&0xff,(UVC_FPS_MAX>>8)&0xff,(UVC_FPS_MAX>>16)&0xff,(UVC_FPS_MAX>>24)&0xff, 
		UVC_FPS_MIN&0xff,(UVC_FPS_MIN>>8)&0xff,(UVC_FPS_MIN>>16)&0xff,(UVC_FPS_MIN>>24)&0xff,
	//SOL4
	0x22, CS_INTERFACE, VS_FRAME_MJPEG, SOL4, 0x01, SOL4_W&0xff,SOL4_W>>8,SOL4_H&0xff,SOL4_H>>8,
		SOL4_MIN_BITRATE&0xff,(SOL4_MIN_BITRATE>>8)&0xff,(SOL4_MIN_BITRATE>>16)&0xff,(SOL4_MIN_BITRATE>>24)&0xff, 
		SOL4_MAX_BITRATE&0xff,(SOL4_MAX_BITRATE>>8)&0xff,(SOL4_MAX_BITRATE>>16)&0xff,(SOL4_MAX_BITRATE>>24)&0xff, 
		SOL4_VIDEO_FRAME_SIZE&0xff,(SOL4_VIDEO_FRAME_SIZE>>8)&0xff,(SOL4_VIDEO_FRAME_SIZE>>16)&0xff,(SOL4_VIDEO_FRAME_SIZE>>24)&0xff,
		UVC_FPS_DEFAULT&0xff,(UVC_FPS_DEFAULT>>8)&0xff,(UVC_FPS_DEFAULT>>16)&0xff,(UVC_FPS_DEFAULT>>24)&0xff,
		0x02, 
		UVC_FPS_MAX&0xff,(UVC_FPS_MAX>>8)&0xff,(UVC_FPS_MAX>>16)&0xff,(UVC_FPS_MAX>>24)&0xff, 
		UVC_FPS_MIN&0xff,(UVC_FPS_MIN>>8)&0xff,(UVC_FPS_MIN>>16)&0xff,(UVC_FPS_MIN>>24)&0xff,
	
	//SOL5
	0x22, CS_INTERFACE, VS_FRAME_MJPEG, SOL5, 0x01, SOL5_W&0xff,SOL5_W>>8,SOL5_H&0xff,SOL5_H>>8, 
		SOL5_MIN_BITRATE&0xff,(SOL5_MIN_BITRATE>>8)&0xff,(SOL5_MIN_BITRATE>>16)&0xff,(SOL5_MIN_BITRATE>>24)&0xff, 
		SOL5_MAX_BITRATE&0xff,(SOL5_MAX_BITRATE>>8)&0xff,(SOL5_MAX_BITRATE>>16)&0xff,(SOL5_MAX_BITRATE>>24)&0xff, 
		SOL5_VIDEO_FRAME_SIZE&0xff,(SOL5_VIDEO_FRAME_SIZE>>8)&0xff,(SOL5_VIDEO_FRAME_SIZE>>16)&0xff,(SOL5_VIDEO_FRAME_SIZE>>24)&0xff,
		UVC_FPS_DEFAULT&0xff,(UVC_FPS_DEFAULT>>8)&0xff,(UVC_FPS_DEFAULT>>16)&0xff,(UVC_FPS_DEFAULT>>24)&0xff,
		0x02, 
		UVC_FPS_MAX&0xff,(UVC_FPS_MAX>>8)&0xff,(UVC_FPS_MAX>>16)&0xff,(UVC_FPS_MAX>>24)&0xff, 
		UVC_FPS_MIN&0xff,(UVC_FPS_MIN>>8)&0xff,(UVC_FPS_MIN>>16)&0xff,(UVC_FPS_MIN>>24)&0xff,
	0x1A, CS_INTERFACE, VS_STILL_IMAGE_FRAME, 0x00, MAX_SOL, 
		SOL1_W&0xff,SOL1_W>>8,SOL1_H&0xff,SOL1_H>>8, 
		SOL2_W&0xff,SOL2_W>>8,SOL2_H&0xff,SOL2_H>>8,
		SOL3_W&0xff,SOL3_W>>8,SOL3_H&0xff,SOL3_H>>8, 
		SOL4_W&0xff,SOL4_W>>8,SOL4_H&0xff,SOL4_H>>8,
		SOL5_W&0xff,SOL5_W>>8,SOL5_H&0xff,SOL5_H>>8, 0x00, 
	0x06, CS_INTERFACE, VS_COLORFORMAT, 0x01, 0x01, 0x04,
 
	0x09, INTERFACE_DESCRIPTOR, DUVC_STRM_INTFS, 0x01, 0x01, CC_VIDEO, SC_VIDEOSTREAMING, 0x00, 0x00, 
	0x07, ENDPOINT_DESCRIPTOR, DEV_TXEP_UVC|0x80, EP_TYPE_ISO|EP_SYNC_ASYNC, ISO_MAX_SIZE_FS&0xff, (ISO_MAX_SIZE_FS>>8)|0x10, 0x01, 
	
	0x09, INTERFACE_DESCRIPTOR, DUVC_STRM_INTFS, 0x02, 0x01, CC_VIDEO, SC_VIDEOSTREAMING, 0x00, 0x00, 
	0x07, ENDPOINT_DESCRIPTOR, DEV_TXEP_UVC|0x80, EP_TYPE_ISO|EP_SYNC_ASYNC, USB_UVC_ISO_MAXSIZE&0xff, (USB_UVC_ISO_MAXSIZE>>8), 0x01, 
	
//}
#if 1
//UAC_CONFIG_DESC_DATA size = 0x08+0x09+UAC_CONTROL_WLENGHT+0x09+0x09+0x07+0x0b+0x09+0x07 = 0x6b
//{
	0x08, IAD_DESC, DUAC_CTL_INTFS, 0x02, CC_AUDIO, 0x00, 0x00, 0x04,  //Index of string descriptor
	0x09, INTERFACE_DESCRIPTOR, DUAC_CTL_INTFS, 0x00, 0x00, CC_AUDIO, SC_AUDIOCONTROL, 0x00, 0x04, 
	#define UAC_CONTROL_WLENGHT		(0x09+0x0c+0x08+0x09)  //0x26
	0x09, CS_INTERFACE, AC_HEADER, 0x00, 0x01, UAC_CONTROL_WLENGHT&0xff, UAC_CONTROL_WLENGHT>>8, 0x01, DUAC_STRM_INTFS, 
	0x0c, CS_INTERFACE, AC_INPUT_TERMINAL, UAC_INPUT_ID, UAC_MICROPHONE&0xff, (UAC_MICROPHONE>>8)&0xff, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 
	0x08, CS_INTERFACE, AC_FEATURE_UNIT, UAC_FEATURE_ID, UAC_INPUT_ID, 0x01, 0x03, 0x00, 
	0x09, CS_INTERFACE, AC_OUTPUT_TERMINAL, UAC_OUTPUT_ID, USB_STREAMING&0xff, (USB_STREAMING>>8)&0xff, 0x00, UAC_FEATURE_ID, 0x00,
	
	0x09, INTERFACE_DESCRIPTOR, DUAC_STRM_INTFS, 0x00, 0x00, CC_AUDIO, SC_AUDIOSTREAMING, 0x00, 0x00, 
	0x09, INTERFACE_DESCRIPTOR, DUAC_STRM_INTFS, 0x01, 0x01, CC_AUDIO, SC_AUDIOSTREAMING, 0x00, 0x00,
	0x07, CS_INTERFACE, AS_GENERAL, UAC_OUTPUT_ID, 0x01, TYPE_I_PCM&0xff, (TYPE_I_PCM>>8)&0xff, 
	0x0b, CS_INTERFACE, AS_FORMATTYPE, FORMAT_TYPE_I, 0x01, 0x02, 0x10, 0x01,USB_MIC_SAMPLE&0xff,(USB_MIC_SAMPLE>>8)&0xff,(USB_MIC_SAMPLE>>16)&0xff,
	0x09, ENDPOINT_DESCRIPTOR, DEV_TXEP_UAC|0x80, EP_TYPE_ISO, USB_MIC_ISO_MAXSIZE&0xff, (USB_MIC_ISO_MAXSIZE>>8)&0xff, 0x04, 0x00, 0x00, //ISO 1ms		2^(4-1) *125us = 1ms
	0x07, CS_ENDPOINT, EP_GENERAL, EP_TYPE_ISO, 0x00, 0x00, 0x00,
//}	
#endif
#if 1
//MSC_CONFIG_DESC size = 0x17
//{
	0x09, INTERFACE_DESCRIPTOR, DMSC_INTFS, 0x00, 0x02, CLASS_MASSSTORAGE, SUBCLASS_SCSI, PROTOCOL_BULKONLY, 0x00,
	0x07, ENDPOINT_DESCRIPTOR, DEV_TXEP_MASS|0x80, EP_TYPE_BULK, BULK_MAX_SIZE_HS&0xff, BULK_MAX_SIZE_HS>>8, 0x01,
	0x07, ENDPOINT_DESCRIPTOR, DEV_RXEP_MASS, EP_TYPE_BULK, BULK_MAX_SIZE_HS&0xff, BULK_MAX_SIZE_HS>>8, 0x01,
//}
#endif
};




/*=================================================================================
USB Language ID Descriptor
=================================================================================*/
ALIGNED(4) u8 const UsbLanguageID[4] =
{
	4,			// Num bytes of this descriptor
	0x03,			// String Descriptor
	0x09,			// Language ID LSB
	0x04			// Language ID MSB
};
/*================================================================================
USB String Descriptors
================================================================================*/
ALIGNED(4) u8 const UsbStrDescManufacturer[16] =
{
	16,				// Num bytes of this descriptor
	0x03,				// String Descriptor
	'G',	0,
	'e',	0,
	'n',	0,
	'e',	0,
	'r',	0,
	'i',	0,
	'c',	0
};
ALIGNED(4) u8 const UsbStrDescProduct_0[22] =
{
	22,
	0x03,
	'M', 0x00,
	'S', 0x00,
	'2', 0x00,
	'0', 0x00,
	'0', 0x00,
	' ', 0x00,
	// ' ', 0x00,
	// ' ', 0x00,
	// ' ', 0x00,
	// ' ', 0x00,
};
ALIGNED(4) u8 const UsbStrDescProduct_1[22] =
{
    22,
    0x03,
    'H', 0x00,
    'D', 0x00,
    ' ', 0x00,
    'a', 0x00,
    'u', 0x00,
    'd', 0x00,
    'i', 0x00,
    'o', 0x00,
    ' ', 0x00,
    ' ', 0x00,
};



ALIGNED(4) u8 const UsbStrDescSerialNumber[30] =
{
	30,			// Num bytes of this descriptor
	3,			// String descriptor
	'2',	0,
	'0',	0,
	'2',	0,
	'1',	0,
	'0',	0,
	'9',	0,
	'0',	0,
	'1',	0,
	'0',	0,
	'0',	0,
	'0',	0,
	'0',	0,
	'0',	0,//v
	'0',	0
};
ALIGNED(4) u8 const DEV_QAULIFIER_DESC_DATA[10]=
{
	10,
	DEVICE_QUALIFIER_DESCRIPTOR,
	0x00,
	0x02,
	0x00,
	0x00,
	0x00,
	EP0_MAX_SIZE,
	1,
	0
};
ALIGNED(4) const u8* const StringDescTbl[] = 
{
	UsbLanguageID,
	UsbStrDescManufacturer,
	UsbStrDescProduct_0,
	UsbStrDescSerialNumber,
	UsbStrDescProduct_1,
	UsbStrDescProduct_1
};

#define USB_REQUEST_DIRECTION          (usb_dev_ctl.request.bmRequestType & 0x80)
#define USB_REQUEST_TYPE               (usb_dev_ctl.request.bmRequestType & 0x60)
#define USB_REQUEST_RECIPIENT          (usb_dev_ctl.request.bmRequestType & 0x1f)
#define USB_REQUEST_REQUEST            (usb_dev_ctl.request.bRequest)
#define USB_REQUEST_ENDPOINT           (usb_dev_ctl.request.wIndex&0xff)
#define USB_REQUEST_FEATURE_SELECTOR   (usb_dev_ctl.request.wValue)
#define USB_REQUEST_DESCRIPTOR_TYPE    ((usb_dev_ctl.request.wValue&0xff00)>>8)
#define USB_REQUEST_DESCRIPTOR_INDEX   (usb_dev_ctl.request.wValue&0xff)
#define USB_REQUEST_LENGTH             (usb_dev_ctl.request.wLength)
/*******************************************************************************
* Function Name  : dusb_stack_init
* Description    : dusb_stack_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dusb_stack_init(void)
{
	memset((u8 *)&usb_dev_ctl, 0, sizeof(USB_DEV_CTL));
}
/*******************************************************************************
* Function Name  : timer_Timer1_Stop
* Description    : timer1 stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dusb_bot_reset(void)
{
	usb_dev_ctl.msc_tx_stall = 0;
    usb_dev_ctl.msc_rx_stall = 0;
    usb_dev_ctl.msc_pipe_stall = 0;
	u32 index_temp 	= XSFR_USB20_SIE_EPS;
    // Descriptor: mass storage reset and flush FIFO data
	XSFR_USB20_SIE_EPS 	= DEV_TXEP_MASS;
	XSFR_USB20_SIE_EPTX_CTRL0 	= (DUSB_EPTX_CLRDATATOG | DUSB_EPTX_FLUSHFIFO);		//Clear Toggle, flush tx FIFO
	XSFR_USB20_SIE_EPTX_CTRL1 	= DUSB_EPTX_SETASTX;	            //Set this EP = TX

	XSFR_USB20_SIE_EPRX_CTRL0 	= (DUSB_EPRX_CLRDATATOG| DUSB_EPRX_FLUSHFIFO);	    //Clear Toggle, flush rx FIFO
	XSFR_USB20_SIE_EPS  	= index_temp;
}
/*******************************************************************************
* Function Name  : timer_Timer1_Stop
* Description    : timer1 stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dusb_check_faddr(void)
{
    if (usb_dev_ctl.set_addr) {
        XSFR_USB20_SIE_DEVADDR = usb_dev_ctl.set_addr;
		usb_dev_ctl.set_addr = 0;
    }
}	
/*******************************************************************************
* Function Name  : timer_Timer1_Stop
* Description    : timer1 stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_clrrx_pkt0(void)
{
	XSFR_USB20_SIE_EPS = 0;
    XSFR_USB20_SIE_EP0_CTRL0 = (DUSB_EP0_SVDOUTPKTRDY | DUSB_EP0_SETUPEND);
    usb_dev_ctl.ep0_state = EP0_END_STATE;
    return true;
}
/*******************************************************************************
* Function Name  : usb_stall_ep
* Description    : usb_stall_ep
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_stall_ep(u8 epnum)
{
    u8 index_bak = XSFR_USB20_SIE_EPS; 
    XSFR_USB20_SIE_EPS = (epnum & 0x7f);
    switch (epnum) {
    case USB20_EP0:
        XSFR_USB20_SIE_EP0_CTRL0 = (DUSB_EP0_SVDOUTPKTRDY | DUSB_EP0_SENDSTALL);    //RxRdy, Stall
        break;
    case DEV_RXEP_MASS:
        usb_dev_ctl.msc_rx_stall = 1;
        XSFR_USB20_SIE_EPRX_CTRL0 = DUSB_EPRX_SENDSTALL;           //Stall
        break;
    case 0x80|DEV_TXEP_MASS:
        usb_dev_ctl.msc_tx_stall = 1;
        XSFR_USB20_SIE_EPTX_CTRL0 = DUSB_EPTX_SENDSTALL;           //Stall
        break;
    }
    XSFR_USB20_SIE_EPS = index_bak; 
}


/*******************************************************************************
* Function Name  : usb_clear_ep
* Description    : usb_clear_ep
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dusb_clear_ep(u8 epnum)
{
    u8 index_bak = XSFR_USB20_SIE_EPS;
    XSFR_USB20_SIE_EPS = (epnum & 0x7f);
    switch (epnum) {
    case DEV_RXEP_MASS:
        usb_dev_ctl.msc_rx_stall = 0;
        XSFR_USB20_SIE_EPRX_CTRL0 = DUSB_EPRX_CLRDATATOG;    //Clear Data Toggle
        break;
    case 0x80|DEV_TXEP_MASS:
        usb_dev_ctl.msc_tx_stall = 0;
        XSFR_USB20_SIE_EPTX_CTRL0 = HUSB_EPTX_CLRDATATOG;    //Clear Data Toggle
        break;
    }
    XSFR_USB20_SIE_EPS = index_bak;
}
/*******************************************************************************
* Function Name  : usb_ep0_get_request
* Description    : usb_ep0_get_request
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dusb_ep0_get_request(void)
{
	memcpy((u8 *)&usb_dev_ctl.request, usb_dev_ctl.ep0_fifo_buf, 8);
	usb_dev_ctl.connect = 1;
#ifdef _DEBG_USB_ENUM__
    debg("request:");
	debgbuf((u8 *)&usb_dev_ctl.request,8);
#endif
}
/*******************************************************************************
* Function Name  : usb_ep0_transfer
* Description    : usb_ep0_transfer
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_transfer(void)
{
    u32 cmd, len;
	//XSFR_USB20_SIE_EPS = 0x00;
    len = usb_dev_ctl.ep0_len;
    if (usb_dev_ctl.ep0_len) {
        if (usb_dev_ctl.ep0_len > usb_dev_ctl.ep0_pkt) {
            len = usb_dev_ctl.ep0_pkt;
        }
        usb_dev_ctl.ep0_len -= len;
        memcpy(usb_dev_ctl.ep0_fifo_buf, usb_dev_ctl.ep0_ptr, len);
		usb_dev_ctl.ep0_ptr += len;
		XSFR_USB20_EPINTLEN = len;
		XSFR_USB20_EPINT    = BIT(0);
	#ifdef _DEBG_USB_ENUM__
		debg("[U-Rsp]:\n");
		debgbuf(usb_dev_ctl.ep0_fifo_buf,len);
	#endif
    }
    cmd = DUSB_EP0_TXPKTRDY;
    if (len != usb_dev_ctl.ep0_pkt) {
        cmd |= DUSB_EP0_DATAEND;
        usb_dev_ctl.ep0_state = EP0_END_STATE;
		//debg("EP0_END_STATE\n");
    }
    XSFR_USB20_SIE_EP0_CTRL0 = cmd;  //DataEnd, TxRdy
	//debgbuf(ep0buf,len);
    return true;
}

/*******************************************************************************
* Function Name  : usb_ep0_tx
* Description    : usb_ep0_tx
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool dusb_ep0_tx(u8* txbuf, u8 len)
{
    XSFR_USB20_SIE_EP0_CTRL0 = DUSB_EP0_SVDOUTPKTRDY;         
	
	//debgbuf(txbuf,len);
	memcpy(usb_dev_ctl.ep0_fifo_buf, txbuf, len);
	XSFR_USB20_EPINTLEN = len;
	XSFR_USB20_EPINT    = BIT(0);
	#ifdef _DEBG_USB_ENUM__
		debg("[U-Rsp]:\n");
		debgbuf(usb_dev_ctl.ep0_fifo_buf,len);
	#endif	
    usb_dev_ctl.ep0_state = EP0_END_STATE;
	XSFR_USB20_SIE_EP0_CTRL0 = DUSB_EP0_DATAEND | DUSB_EP0_TXPKTRDY;  //DataEnd, TxRdy
    return true;
}
/*******************************************************************************
* Function Name  : usb_ep0_receive
* Description    : usb_ep0_receive
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dusb_ep0_receive(void)
{	
	if(NULL != usb_dev_ctl.pSetReqCallback) 
		(*usb_dev_ctl.pSetReqCallback)(usb_dev_ctl.ep0_fifo_buf);
	usb_dev_ctl.pSetReqCallback = NULL;	
    dusb_ep0_clrrx_pkt0();
}
/*******************************************************************************
* Function Name  : usb_ep0_receive
* Description    : usb_ep0_receive
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ep0_recieve_set(SETREQ_FUNC callback)
{
	usb_dev_ctl.ep0_state = EP0_RX_STATE;
	usb_dev_ctl.pSetReqCallback= callback;
	XSFR_USB20_SIE_EP0_CTRL0 = DUSB_EP0_SVDOUTPKTRDY;           //清除RxRdy	
}
/*******************************************************************************
* Function Name  : usb_ep0_get_status
* Description    : usb_ep0_get_status
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_get_status(void)
{
    if (0 == USB_REQUEST_DIRECTION) {     
        return false;
    }
    if (USB_DEFAULT_STATE == usb_dev_ctl.cfg_state) {
        return false;
    }
    usb_dev_ctl.ep0_fifo_buf[0] = 0;
	usb_dev_ctl.ep0_fifo_buf[1] = 0;
    switch (USB_REQUEST_RECIPIENT) {      
	
		case REQUEST_TO_DEVICE:                    
			usb_dev_ctl.ep0_fifo_buf[1] = (REMOTE_WAKEUP<<1)|SELF_POWER;
			break;
		
		case REQUEST_TO_INTERFACE:
			if (USB_ADDRESS_STATE == usb_dev_ctl.cfg_state) {
				return false;
			}
			break;                                
		case REQUEST_TO_ENDPOINT:
			if(0 == USB_REQUEST_ENDPOINT)
			{
				if(XSFR_USB20_SIE_EP0_CTRL0 & DUSB_EP0_SENTSTALL)
				{
					usb_dev_ctl.ep0_fifo_buf[1] = 1;
				}
			}else
			{
				if ((USB_REQUEST_ENDPOINT & 0x7f) <= USB20_MAX_EPX) {
					if (USB_REQUEST_ENDPOINT & 0x80)
					{
						if (usb_dev_ctl.msc_tx_stall) {
							usb_dev_ctl.ep0_fifo_buf[1] = 1;
						}
					}else
					{
						if (usb_dev_ctl.msc_rx_stall) {
							usb_dev_ctl.ep0_fifo_buf[1] = 1;
						}
					}
				}
			}
			break;
		default:
			return false;
    }
    return dusb_ep0_tx(usb_dev_ctl.ep0_fifo_buf,2);
}
/*******************************************************************************
* Function Name  : usb_ep0_clr_feature
* Description    : usb_ep0_clr_feature
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
//Clear Feature
static bool dusb_ep0_clr_feature(void)
{
    if (USB_DEFAULT_STATE != usb_dev_ctl.cfg_state &&
      REQUEST_TO_ENDPOINT == USB_REQUEST_RECIPIENT &&
      ENDPOINT_STALL == USB_REQUEST_FEATURE_SELECTOR &&
      !(USB_ADDRESS_STATE == usb_dev_ctl.cfg_state && USB_REQUEST_ENDPOINT)) {

        dusb_clear_ep(USB_REQUEST_ENDPOINT);
        return dusb_ep0_clrrx_pkt0();
    }
    return false;
}
/*******************************************************************************
* Function Name  : usb_ep0_set_feature
* Description    : usb_ep0_set_feature
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_set_feature(void)
{
    if (REQUEST_TO_ENDPOINT == USB_REQUEST_RECIPIENT &&
      ENDPOINT_STALL == USB_REQUEST_FEATURE_SELECTOR &&
      !(USB_ADDRESS_STATE == usb_dev_ctl.cfg_state && USB_REQUEST_ENDPOINT)) {

        dusb_stall_ep(USB_REQUEST_ENDPOINT);
        return dusb_ep0_clrrx_pkt0();
    }
    return false;
}
/*******************************************************************************
* Function Name  : usb_ep0_set_address
* Description    : usb_ep0_set_address 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_set_address(void)
{
	if(usb_dev_ctl.cfg_state == USB_CONFIG_STATE)
		return false;
	if(USB_REQUEST_DESCRIPTOR_INDEX == 0)
	{
		usb_dev_ctl.cfg_state = USB_DEFAULT_STATE;
	}else
	{
		usb_dev_ctl.cfg_state = USB_ADDRESS_STATE;
	}
    usb_dev_ctl.set_addr = USB_REQUEST_DESCRIPTOR_INDEX;
    return dusb_ep0_clrrx_pkt0();
}
/*******************************************************************************
* Function Name  : usb_ep0_get_configuration
* Description    : usb_ep0_get_configuration
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
//Get Configuration
static bool dusb_ep0_get_configuration(void)
{
    if (USB_DEFAULT_STATE != usb_dev_ctl.cfg_state) {
        usb_dev_ctl.ep0_fifo_buf[0] = usb_dev_ctl.cfg_value;
        return dusb_ep0_tx(usb_dev_ctl.ep0_fifo_buf,1);
    }
    return false;
}
/*******************************************************************************
* Function Name  : usb_ep0_set_configuration
* Description    : usb_ep0_set_configuration
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_set_configuration(void)
{
	
    if (USB_DEFAULT_STATE != usb_dev_ctl.cfg_state) {
        XSFR_USB20_SIE_COM_INTEN = (USB_RESET | USB_SUSPEND/*| USB_SOF*/);  //Enable Suspend int
		usb_dev_ctl.cfg_value = USB_REQUEST_DESCRIPTOR_INDEX;
		if(usb_dev_ctl.cfg_value)
			usb_dev_ctl.cfg_state = USB_CONFIG_STATE;
		else
			usb_dev_ctl.cfg_state = USB_DEFAULT_STATE;
		dusb_bot_reset(); 	
    }
    
    return dusb_ep0_clrrx_pkt0();
}
/*******************************************************************************
* Function Name  : usb_ep0_get_interface
* Description    : usb_ep0_get_interface
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
//Get Interface
static bool dusb_ep0_get_interface(void)
{
    if (USB_CONFIG_STATE == usb_dev_ctl.cfg_state) {
        usb_dev_ctl.ep0_fifo_buf[0] = 0;//usb_config_all_descriptor[12];
//		#warning "usb_config_all_descriptor"
        return dusb_ep0_tx(usb_dev_ctl.ep0_fifo_buf,1);
    }
    return false;
}
/*******************************************************************************
* Function Name  : dusb_ep0_set_interface
* Description    : dusb_ep0_set_interface
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
//Set Interface
static bool dusb_ep0_set_interface(void)
{
    if (USB_CONFIG_STATE == usb_dev_ctl.cfg_state) 
	{
		if(USB_REQUEST_ENDPOINT == usb_dev_ctl.uvc_tx_interface)
		{
			if(USB_REQUEST_DESCRIPTOR_INDEX)
			{
				deg_Printf("-UVC ON-\n");
				uvc_start();
			}
			else
			{
				deg_Printf("-UVC OFF-\n");
				uvc_stop();
			}
		}
		else if(USB_REQUEST_ENDPOINT == usb_dev_ctl.uac_tx_interface)
		{
			if(USB_REQUEST_DESCRIPTOR_INDEX)
			{
				deg_Printf("-UAC ON-\n");
				//uac_start();
			}
			else
			{
				deg_Printf("-UAC OFF-\n");
				uac_stop();
			}
		}			
        return dusb_ep0_clrrx_pkt0();
		
    }
    return false;
}


/*******************************************************************************
* Function Name  : dusb_ep0_descriptor
* Description    : dusb_ep0_descriptor
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_descriptor(u8 *buf, u16 len)
{
    if (len > USB_REQUEST_LENGTH) {
        len = USB_REQUEST_LENGTH;
    }

    usb_dev_ctl.ep0_ptr = (u8 *)buf;
    usb_dev_ctl.ep0_len = len;
    usb_dev_ctl.ep0_state = EP0_TX_STATE;
    XSFR_USB20_SIE_EP0_CTRL0 = DUSB_EP0_SVDOUTPKTRDY;        
	//debg("usb_ep0_transfer\n");
    return dusb_ep0_transfer();
}
/*******************************************************************************
* Function Name  : dusb_ep0_get_descriptor
* Description    : dusb_ep0_get_descriptor
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_get_descriptor(void)
{
    switch (USB_REQUEST_DESCRIPTOR_TYPE) {       //Request Descriptor Type
		case DEVICE_DESCRIPTOR:
			return dusb_ep0_descriptor((u8 *)usb_dev_ctl.pdev, usb_dev_ctl.devlen);

		case CONFIGURATION_DESCRIPTOR:
		case OTHER_SPEED_CONFIG_DESCRIPTOR:
			if( ((hx330x_usb20_HighSpeed() == true) && (USB_REQUEST_DESCRIPTOR_TYPE == CONFIGURATION_DESCRIPTOR))||
			 ((hx330x_usb20_HighSpeed() == false) && (USB_REQUEST_DESCRIPTOR_TYPE != CONFIGURATION_DESCRIPTOR)) )
			{
				#if 1
				if(usb_dev_ctl.pcfg == dusb_msc_cfgdsc)
				{
					usb_dev_ctl.pcfg[8] = 0x32;
					
				}else
				{
					//usb_dev_ctl.pcfg[362] = (ISO_MAX_SIZE_FS>>8)|0x10;
				}
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-10] = BULK_MAX_SIZE_HS&0xff;
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-9] = BULK_MAX_SIZE_HS>>8;
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-3] = BULK_MAX_SIZE_HS&0xff;
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-2] = BULK_MAX_SIZE_HS>>8;
				#endif
			}else
			{
				#if 1
				if(usb_dev_ctl.pcfg == dusb_msc_cfgdsc)
				{
					usb_dev_ctl.pcfg[8] = 0x32/2;
					
				}else
				{
					//usb_dev_ctl.pcfg[362] = (ISO_MAX_SIZE_FS>>8);
				}
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-10] = BULK_MAX_SIZE_FS&0xff;
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-9] = BULK_MAX_SIZE_FS>>8;
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-3] = BULK_MAX_SIZE_FS&0xff;
				usb_dev_ctl.pcfg[usb_dev_ctl.cfglen-2] = BULK_MAX_SIZE_FS>>8;	
				#endif
			}
			return dusb_ep0_descriptor((u8 *)usb_dev_ctl.pcfg, usb_dev_ctl.cfglen);

		
     case STRING_DESCRIPTOR:
			if(USB_REQUEST_DESCRIPTOR_INDEX<6)
			{
				return dusb_ep0_descriptor((u8 *)StringDescTbl[USB_REQUEST_DESCRIPTOR_INDEX], StringDescTbl[USB_REQUEST_DESCRIPTOR_INDEX][0]);
			}
			else
			{
				return false;
			}
    case DEVICE_QUALIFIER_DESCRIPTOR:
        return dusb_ep0_descriptor((u8 *)DEV_QAULIFIER_DESC_DATA, sizeof(DEV_QAULIFIER_DESC_DATA));

    default:
        return false;
    }
}

/*******************************************************************************
* Function Name  : dusb_ep0_standard
* Description    : dusb_ep0_standard
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_standard(void)
{
	//deg_Printf(".request:%x\n",USB_REQUEST_REQUEST); 
    switch (USB_REQUEST_REQUEST) {
		case SC_GET_STATUS:     //Get Status
			return dusb_ep0_get_status();
		case SC_CLEAR_FEATURE:     //Clear Feature
			return dusb_ep0_clr_feature();
		case SC_SET_FEATURE:     //Set Feature
			return dusb_ep0_set_feature();
		case SC_SET_ADDRESS:     //Set Address
			//deg_Printf("-usb set adr\n");
			return dusb_ep0_set_address();
		case SC_GET_DESCRIPTOR:     //Get Descriptor
			return dusb_ep0_get_descriptor();
		case SC_GET_CONFIGURATION:     //Get Configuration
			return dusb_ep0_get_configuration();
		case SC_SET_CONFIGURATION:     //Set Configuration
			return dusb_ep0_set_configuration();
		case SC_GET_INTERFACE:     //Get Interface
			return dusb_ep0_get_interface();
		case SC_SET_INTERFACE:     //Set Interface
			return dusb_ep0_set_interface();
		default:
			return false;
    }
}
/*******************************************************************************
* Function Name  : dusb_ep0_class_get_lun
* Description    : dusb_ep0_class_get_lun
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_class_get_lun(void)
{
    usb_dev_ctl.ep0_fifo_buf[0] = 0;
    return dusb_ep0_tx(usb_dev_ctl.ep0_fifo_buf,1);
}
/*******************************************************************************
* Function Name  : dusb_ep0_class_Reset_Setup
* Description    : dusb_ep0_class_Reset_Setup
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_class_Reset_Setup(void)
{
	if (USB_CONFIG_STATE == usb_dev_ctl.cfg_state) {
        return dusb_ep0_clrrx_pkt0();
    }else
	{
		dusb_bot_reset();
		return false;
	}
}
#if HAL_CFG_PQTOOL_EN
bool dusb_ep0_class_ram_wr(u8 * rxbuf)
{
	u8 * dst = (u8 *)usb_dev_ctl.addr32;
    memcpy((u8  *)dst, (u8 *)rxbuf, USB_REQUEST_LENGTH);
    return true;
}

bool dusb_ep0_class_addr_wr(u8 * rxbuf)
{
	u8 * dst = (u8 *)(&usb_dev_ctl.addr32);
    memcpy((u8  *)dst, (u8 *)rxbuf, USB_REQUEST_LENGTH);
    return true;
}
#endif
/*******************************************************************************
* Function Name  : dusb_ep0_class
* Description    : dusb_ep0_class
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/	
static bool dusb_ep0_class(void)
{
	#define __UVCID__(a,b) ((a << 8)|(b << 0))
	//deg_Printf("index:%x\n",usb_dev_ctl.request.wIndex);
	//debgbuf(&usb_dev_ctl.request,8);
	if((usb_dev_ctl.request.wValue == CS_VENDOR_RESET) && (usb_dev_ctl.request.wIndex == 0))
	{
		usb_dev_ctl.returnmaskcombo = 1;
		return dusb_ep0_clrrx_pkt0();
	}
	else if((usb_dev_ctl.request.wValue == CS_VENDOR_READ_IC_INF) && (usb_dev_ctl.request.wIndex == 0))
	{
		return dusb_ep0_tx((u8*)SDK_CHIP_INF,USB_REQUEST_LENGTH);
	}
#if HAL_CFG_PQTOOL_EN
	else if((usb_dev_ctl.request.wValue == CS_VENDOR_RAM) && (usb_dev_ctl.request.wIndex == 0))
	{
		if (USB_REQUEST_REQUEST == 0x81)
			return dusb_ep0_tx((u8*)usb_dev_ctl.addr32, USB_REQUEST_LENGTH);
		else if (USB_REQUEST_REQUEST == 0x01)
		{
			dusb_ep0_recieve_set(dusb_ep0_class_ram_wr);
			return true;
		}
		return false;
	}
	else if((usb_dev_ctl.request.wValue == CS_VENDOR_RUN) && (usb_dev_ctl.request.wIndex == 0))
	{
		void (*func)(void) = (void (*)(void))usb_dev_ctl.addr32;
		func();
		return dusb_ep0_clrrx_pkt0();
	}
	else if((usb_dev_ctl.request.wValue == CS_VENDOR_WR_ADDR) && (usb_dev_ctl.request.wIndex == 0))
	{
		dusb_ep0_recieve_set(dusb_ep0_class_addr_wr);
		return true;
	}
	else if((usb_dev_ctl.request.wValue == CS_VENDOR_READ_SDK_INF))
	{
		return dusb_ep0_tx(pqtool_get_sdk_info() + usb_dev_ctl.request.wIndex, USB_REQUEST_LENGTH);
	}
#endif
	else if (SC_GET_MAX_LUN == usb_dev_ctl.request.bmRequestType && 0xfe == USB_REQUEST_REQUEST) {
		//Class Get Max Lun
		//debg("SC_GET_MAX_LUN\n");
		return dusb_ep0_class_get_lun();
	}	
	else if((usb_dev_ctl.request.bmRequestType == SC_BULK_ONLY_RESET) &&(USB_REQUEST_REQUEST == 0xff))
	{
		//debg("SC_BULK_ONLY_RESET\n");
		return dusb_ep0_class_Reset_Setup();
	}
	else if(usb_dev_ctl.request.wIndex == __UVCID__(UVC_PROCESS_ID,DUVC_CTL_INTFS)){
		//hum	
		//debg("uvc_unit_ctl_hal\n");
		return uvc_unit_ctl_hal(USB_REQUEST_DESCRIPTOR_TYPE,USB_REQUEST_REQUEST,USB_REQUEST_LENGTH);
	}
	else if(usb_dev_ctl.request.wIndex == __UVCID__(0,DUVC_STRM_INTFS)){
		//hum
		//debg("uvc_probe_ctl_hal\n");	
		return uvc_probe_ctl_hal(USB_REQUEST_DESCRIPTOR_TYPE,USB_REQUEST_REQUEST,USB_REQUEST_LENGTH);
	}
	else if(usb_dev_ctl.request.wIndex == __UVCID__(UAC_FEATURE_ID,DUAC_CTL_INTFS))
	{
		//debg("uac_unit_ctl_hal\n");
		return  uac_unit_ctl_hal(USB_REQUEST_DESCRIPTOR_TYPE,USB_REQUEST_REQUEST,USB_REQUEST_LENGTH);
	}		
	else if((usb_dev_ctl.request.bmRequestType == 0x22) &&(USB_REQUEST_REQUEST == 0x01))
	{
		//debg("UacHandleToStreaming\n");
		return  UacHandleToStreaming(USB_REQUEST_DESCRIPTOR_TYPE,USB_REQUEST_REQUEST,USB_REQUEST_LENGTH);
	}	
	return false;
		
}

/*******************************************************************************
* Function Name  : dusb_ep0_request
* Description    : dusb_ep0_request
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dusb_ep0_request(void)
{
    if (0 == USB_REQUEST_TYPE) {
        return dusb_ep0_standard();          
    } else if (0x20 == USB_REQUEST_TYPE) {
        return dusb_ep0_class();            
    }
    return false;
}
/*******************************************************************************
* Function Name  : dusb_ep0_process
* Description    : dusb_ep0_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ep0_process(void)
{
    //USB EP0
	//deg_Printf("ep0_isr\n");
    u32 index_temp = XSFR_USB20_SIE_EPS;
	u32 epincval_temp = XSFR_USB20_EPINTLEN;
    XSFR_USB20_SIE_EPS = 0;                 
    dusb_check_faddr();               
    u32 csr0 = XSFR_USB20_SIE_EP0_CTRL0;
    if (csr0 & DUSB_EP0_SENTSTALL) {                        //SentStall
        XSFR_USB20_SIE_EP0_CTRL0 = 0;
        usb_dev_ctl.ep0_state = EP0_IDLE_STATE;
    }

    if (csr0 & DUSB_EP0_SETUPEND) {                        //SetupEnd
		XSFR_USB20_SIE_EP0_CTRL0 = DUSB_EP0_SVDSETUPEND;
        usb_dev_ctl.ep0_state = EP0_IDLE_STATE;
    }

    if (usb_dev_ctl.ep0_state == EP0_END_STATE) {
        usb_dev_ctl.ep0_state = EP0_IDLE_STATE;
    }
	//deg_Printf("- csr0:%x\n",csr0);
	
    switch (usb_dev_ctl.ep0_state) {
		case EP0_IDLE_STATE:
			if (csr0 & DUSB_EP0_RXPKTRDY) {                    //EP0 RxRdy
				dusb_ep0_get_request();          
				if (!dusb_ep0_request()) {      
					dusb_stall_ep(0x00);
				}
				
			}
			break;
		case EP0_TX_STATE:
			dusb_ep0_transfer();
			break;
		case EP0_RX_STATE:
			dusb_ep0_receive();
			break;
		default:
			break;
    }
    XSFR_USB20_SIE_EPS = index_temp;
	XSFR_USB20_EPINTLEN = epincval_temp;
}
/*******************************************************************************
* Function Name  : enum_epx_cfg
* Description    : enum_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ep0_cfg(void)
{
	usb_dev_ctl.ep0_fifo_buf 		= (u8*)_USB20_EP0_FIFO_;//_ep0_fifo_;
    usb_dev_ctl.ep0_pkt 			= EP0_MAX_SIZE;    //初始包大小
	usb_dev_ctl.uvc_tx_interface 	= DUVC_STRM_INTFS;
	usb_dev_ctl.uac_tx_interface    = DUAC_STRM_INTFS;
	
	XSFR_USB20_EP0_ADDR         		= (u32)_USB20_EP0_FIFO_;
	//debg("XSFR_USB20_SDR_DMA_EN:%x\n",XSFR_USB20_SDR_DMA_EN);
	XSFR_USB20_SDR_DMA_EN            = 0;
}
/*******************************************************************************
* Function Name  : dusb_cfg_reg
* Description    : dusb_cfg_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_cfg_reg(u8 type)
{
	dusb_stack_init();
	if(USB_DEVTYPE_MSC == type){
		usb_dev_ctl.pdev 	= (u8*)dusb_msc_devdsc;
		usb_dev_ctl.devlen 	= sizeof(dusb_msc_devdsc);
		usb_dev_ctl.pcfg	= (u8*)dusb_msc_cfgdsc;
		usb_dev_ctl.cfglen 	= sizeof(dusb_msc_cfgdsc); 
		deg_Printf("-DUSB MSC TYPE-\n");
	}
	else if(USB_DEVTYPE_COMBINE == type){
		usb_dev_ctl.pdev 	= (u8*)dusb_com_devdsc;
		usb_dev_ctl.devlen 	= sizeof(dusb_com_devdsc);
		usb_dev_ctl.pcfg 	= (u8*)dusb_com_cfgdsc;
		usb_dev_ctl.cfglen 	= sizeof(dusb_com_cfgdsc); 
		deg_Printf("-DUSB COMBINE TYPE-\n");		
	}
	
}

