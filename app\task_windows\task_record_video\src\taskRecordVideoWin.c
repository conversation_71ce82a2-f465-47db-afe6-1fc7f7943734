/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	VIDEO_MODE_ID=0,
	VIDEO_REC_TIME_ID,
	VIDEO_RESOLUTION_ID,

	VIDEO_POWERON_TIME_ID,
	VIDEO_BATERRY_ID,

	VIDEO_SYSTIME_ID,

	VIDEO_IRLED_ID,
	VIDEO_MD_ID,// motion detect
	VIDEO_MONITOR_ID, // parking monitoring

	VIDEO_LOCK_ID,
	VIDEO_SD_ID,
	VIDEO_MIC_ID,

	VIDEO_LED_ID,
	VIDEO_LED_LEVEL_ID,
	VIDEO_SCALER_ID,
	VIDEO_TAKE_SUCCESS_ID,
	VIDEO_NO_INSERT_SD,
	VIDEO_REC_RED_DOT,



	VIDEO_MAX_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor recordVideoWin[] =
{
	createFrameWin( 						Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent, WIN_ABS_POS),

	createImageIcon(VIDEO_MODE_ID,      	Rx(0),   Ry(0),   Rw(34),  Rh(32),  R_ID_ICON_MTRECORD, 	ALIGNMENT_LEFT),

	createImageIcon(VIDEO_REC_RED_DOT,      	Rx(5),   Ry(200),   Rw(25),  Rh(25),  R_ID_ICON_RED_DOT, 	ALIGNMENT_LEFT),
	createStringIcon(VIDEO_REC_TIME_ID,  	Rx(25),  Ry(200),   Rw(75),  Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),

	//createStringIcon(VIDEO_RESOLUTION_ID,	Rx(105), Ry(0),   Rw(40),  Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_White,	DEFAULT_FONT),

	//createStringIcon(VIDEO_POWERON_TIME_ID,	Rx(250), Ry(0),   Rw(45),  Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createImageIcon(VIDEO_BATERRY_ID,    	Rx(290), Ry(0),   Rw(45),  Rh(32), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),

	createStringIcon(VIDEO_SYSTIME_ID,      Rx(10),   Ry(215), Rw(220), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),

	//createImageIcon(VIDEO_IRLED_ID,     	Rx(270), Ry(165), Rw(25),  Rh(25), 	R_ID_ICON_MTIROFF,		ALIGNMENT_CENTER),

	//createImageIcon(VIDEO_MD_ID,        	Rx(270), Ry(190), Rw(25),  Rh(25), 	R_ID_ICON_MTMOTION,		ALIGNMENT_CENTER),
	//createImageIcon(VIDEO_MONITOR_ID,   	Rx(270), Ry(215), Rw(25),  Rh(25), 	R_ID_ICON_MTPARKOFF,	ALIGNMENT_CENTER),

	//createImageIcon(VIDEO_LOCK_ID,      	Rx(295), Ry(165), Rw(25),  Rh(25), 	R_ID_ICON_MTLOCK,		ALIGNMENT_CENTER),
	createImageIcon(VIDEO_SD_ID,        	Rx(250), Ry(0), Rw(32),  Rh(32), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	//createImageIcon(VIDEO_MIC_ID,       	Rx(295), Ry(215), Rw(25),  Rh(25), R_ID_ICON_MTMICOFF,		ALIGNMENT_CENTER),
	createStringIcon(VIDEO_SCALER_ID,		Rx(270), Ry(215), Rw(45),  Rh(35),	RAM_ID_MAKE(" "),	ALIGNMENT_CENTER, R_ID_PALETTE_White,	DEFAULT_FONT),



	// createImageIcon(VIDEO_LED_ID,			Rx(237), Ry(0), Rw(30),  Rh(30),		R_ID_ICON_CG_LED3,	ALIGNMENT_CENTER),


	widgetEnd(),
};
/*******************************************************************************
* Function Name  : videoRemainTimeShow
* Description    : videoRemainTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRemainTimeShow(winHandle handle)
{
	uiWinSetStrInfor(winItem(handle,VIDEO_REC_TIME_ID),DEFAULT_FONT,ALIGNMENT_LEFT,R_ID_PALETTE_White);
	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),RAM_ID_MAKE(task_com_rec_remain_time_str()));
}
/*******************************************************************************
* Function Name  : videoRecTimeShow
* Description    : videoRecTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRecTimeShow(winHandle handle)
{
	uiWinSetStrInfor(winItem(handle,VIDEO_REC_TIME_ID),	DEFAULT_FONT,	ALIGNMENT_LEFT,R_ID_PALETTE_Red);
	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),	RAM_ID_MAKE(task_com_rec_show_time_str()));
}
/*******************************************************************************
* Function Name  : videoResolutionShow
* Description    : videoResolutionShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoResolutionShow(winHandle handle)
{
	switch(user_config_get(CONFIG_ID_RESOLUTION))
	{
		case R_ID_STR_RES_HD:  uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("HD")); break;
		case R_ID_STR_RES_FHD: uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("FHD")); break;
		default:			   uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("???")); break;
	}
}
/*******************************************************************************
* Function Name  : videoPoweOnTimeShow
* Description    : videoPoweOnTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoPoweOnTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_POWERON_TIME_ID),RAM_ID_MAKE(task_com_powerOnTime_str()));
}
/*******************************************************************************
* Function Name  : videoBaterryShow
* Description    : videoBaterryShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,VIDEO_BATERRY_ID),batid);

}
/*******************************************************************************
* Function Name  : videoSysTimeShow
* Description    : videoSysTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoSysTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_SYSTIME_ID),RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())));
}
/*******************************************************************************
* Function Name  : videoMonitorShow
* Description    : videoMonitorShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoIrLedShow(winHandle handle)
{
	if(hardware_setup.ir_led_en)
	{
		if(user_config_get(CONFIG_ID_IR_LED)==R_ID_STR_COM_OFF)
			uiWinSetResid(winItem(handle,VIDEO_IRLED_ID),R_ID_ICON_MTIROFF);
		else
			uiWinSetResid(winItem(handle,VIDEO_IRLED_ID),R_ID_ICON_MTIRON);
	}else
	{
		uiWinSetVisible(winItem(handle,VIDEO_IRLED_ID),0);
	}
}
/*******************************************************************************
* Function Name  : videoMDShow
* Description    : videoMDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoMDShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_MOTIONDECTION) == R_ID_STR_COM_ON)
	{
	    uiWinSetVisible(winItem(handle,VIDEO_MD_ID),1);
		uiWinSetResid(winItem(handle,VIDEO_MD_ID),R_ID_ICON_MTMOTION);

	}
	else
		uiWinSetVisible(winItem(handle,VIDEO_MD_ID),0);
}
/*******************************************************************************
* Function Name  : videoMonitorShow
* Description    : videoMonitorShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoMonitorShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_PARKMODE)==R_ID_STR_COM_ON)
		uiWinSetResid(winItem(handle,VIDEO_MONITOR_ID),R_ID_ICON_MTPARKON);
	else
		uiWinSetResid(winItem(handle,VIDEO_MONITOR_ID),R_ID_ICON_MTPARKOFF);
}
/*******************************************************************************
* Function Name  : videoLockShow
* Description    : videoLockShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoLockShow(winHandle handle)
{
	if(SysCtrl.dev_stat_gsensorlock)
	{
	    uiWinSetVisible(winItem(handle,VIDEO_LOCK_ID),1);
		uiWinSetResid(winItem(handle,VIDEO_LOCK_ID),R_ID_ICON_MTLOCK);

	}
	else
		uiWinSetVisible(winItem(handle,VIDEO_LOCK_ID),0);
}
/*******************************************************************************
* Function Name  : videoSDShow
* Description    : videoSDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoSDShow(winHandle handle)
{
	task_com_sdlist_scan(0, 1);

	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,VIDEO_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,VIDEO_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : videoMicShow
* Description    : videoMicShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoMicShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_AUDIOREC)==R_ID_STR_COM_OFF)
		uiWinSetResid(winItem(handle,VIDEO_MIC_ID),R_ID_ICON_MTMICOFF);
	else
		uiWinSetResid(winItem(handle,VIDEO_MIC_ID),R_ID_ICON_MTMICON);
}

UNUSED static void videoLedShow(winHandle handle)
{

	resID ledid;
	switch(SysCtrl.led_pwm_level)
	{
		case 0: ledid = R_ID_ICON_CG_LED0; break;
		case 1: ledid = R_ID_ICON_CG_LED1;break;
		case 2: ledid = R_ID_ICON_CG_LED2; break;
		case 3: ledid = R_ID_ICON_CG_LED3; break;
		default:
									 break;
	}
	uiWinSetResid(winItem(handle,VIDEO_LED_ID),ledid);


}

UNUSED static void videoscalerShow(winHandle handle)
{
	resID scalerid;
	switch(SysCtrl.lcd_scaler_level_uishow)
	{
		case 0: uiWinSetResid(winItem(handle,VIDEO_SCALER_ID),RAM_ID_MAKE("1.0X")); break;
		case 1: uiWinSetResid(winItem(handle,VIDEO_SCALER_ID),RAM_ID_MAKE("2.0X")); break;
		case 2: uiWinSetResid(winItem(handle,VIDEO_SCALER_ID),RAM_ID_MAKE("3.0X")); break;
		case 3: uiWinSetResid(winItem(handle,VIDEO_SCALER_ID),RAM_ID_MAKE("4.0X")); break;
		default:
									 break;
	}



}

/*******************************************************************************
* Function Name  : videoRedDotBlinkShow
* Description    : videoRedDotBlinkShow - 控制录制红点的闪烁显示
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void videoRedDotBlinkShow(winHandle handle, u32 visable)
{
	static u32 blinkFlag = 0;
	
	// 录制时闪烁
	if(blinkFlag)
	{
		blinkFlag ^= 1;
	}
	else
	{
		blinkFlag =  1;
	}
	
	// 非录制时隐藏
	uiWinSetVisible(winItem(handle, VIDEO_REC_RED_DOT), (blinkFlag & visable));
	
}







