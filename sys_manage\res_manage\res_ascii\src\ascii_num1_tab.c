/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : ascii_tab, font num2 :16 * 32
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
// 空格 ' '
const unsigned char ascii_num1_32[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 感叹号 '!'
const unsigned char ascii_num1_33[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 井号 '#'
const unsigned char ascii_num1_35[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x28, 0x28, 0xF8, 0x50, 0xF8, 0xA0, 0xA0, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 美元 '$'
const unsigned char ascii_num1_36[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0xA8, 0xE0, 0x38, 0x28, 0xA8, 0x70, 0x20, 0x00, 0x00, 0x00, 0x00
};
// 百分号 '%'
const unsigned char ascii_num1_37[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x22, 0x54, 0x54, 0x2A, 0x0D, 0x15, 0x12, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 与符号 '&'
const unsigned char ascii_num1_38[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x38, 0x48, 0x30, 0x60, 0x96, 0x8C, 0x76, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 单引号 '\''
const unsigned char ascii_num1_39[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x80, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 左括号 '('
const unsigned char ascii_num1_40[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x20, 0x40, 0x80, 0x80, 0x80, 0x80, 0x80, 0x40, 0x20, 0x00, 0x00, 0x00
};
// 右括号 ')'
const unsigned char ascii_num1_41[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x80, 0x40, 0x20, 0x20, 0x20, 0x20, 0x20, 0x40, 0x80, 0x00, 0x00, 0x00
};
// 星号 '*'
const unsigned char ascii_num1_42[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0xE0, 0x40, 0xA0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 加号 '+'
const unsigned char ascii_num1_43[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x20, 0x20, 0xF8, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 逗号 ','
const unsigned char ascii_num1_44[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00
};
// 减号 '-'
const unsigned char ascii_num1_45[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 句号 '.'
const unsigned char ascii_num1_46[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 斜杠 '/'
const unsigned char ascii_num1_47[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x20, 0x20, 0x40, 0x40, 0x40, 0x80, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '0'
const unsigned char ascii_num1_48[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '1'
const unsigned char ascii_num1_49[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x10, 0x30, 0x50, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '2'
const unsigned char ascii_num1_50[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x88, 0x08, 0x10, 0x20, 0x40, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '3'
const unsigned char ascii_num1_51[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x88, 0x08, 0x30, 0x08, 0x88, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '4'
const unsigned char ascii_num1_52[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x18, 0x28, 0x48, 0x88, 0xFC, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '5'
const unsigned char ascii_num1_53[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x78, 0x40, 0xF0, 0x88, 0x08, 0x88, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '6'
const unsigned char ascii_num1_54[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x88, 0xF0, 0x88, 0x88, 0x88, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '7'
const unsigned char ascii_num1_55[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0xF8, 0x10, 0x10, 0x20, 0x20, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '8'
const unsigned char ascii_num1_56[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x88, 0x88, 0x70, 0x88, 0x88, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 数字 '9'
const unsigned char ascii_num1_57[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x88, 0x88, 0x88, 0x78, 0x88, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 冒号 ':'
const unsigned char ascii_num1_58[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 分号 ';'
const unsigned char ascii_num1_59[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00
};
// 小于号 '<'
const unsigned char ascii_num1_60[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x08, 0x30, 0x40, 0x30, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 等于号 '='
const unsigned char ascii_num1_61[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 大于号 '>'
const unsigned char ascii_num1_62[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x40, 0x30, 0x08, 0x30, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 问号 '?'
const unsigned char ascii_num1_63[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x78, 0x84, 0x04, 0x18, 0x10, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00
};
// '@'
const unsigned char ascii_num1_64[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x1F, 0x20, 0x4D, 0x53, 0x52, 0x52, 0x4F, 0x20, 0x1F, 0x00, 0x00, 0x00
};
// 'A'
const unsigned char ascii_num1_65[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x10, 0x28, 0x28, 0x44, 0x7C, 0x44, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'B'
const unsigned char ascii_num1_66[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x78, 0x44, 0x44, 0x7C, 0x44, 0x44, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'C'
const unsigned char ascii_num1_67[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x38, 0x44, 0x40, 0x40, 0x40, 0x44, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'D'
const unsigned char ascii_num1_68[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x70, 0x48, 0x44, 0x44, 0x44, 0x48, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'E'
const unsigned char ascii_num1_69[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x7C, 0x40, 0x40, 0x7C, 0x40, 0x40, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'F'
const unsigned char ascii_num1_70[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x78, 0x40, 0x40, 0x70, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'G'
const unsigned char ascii_num1_71[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x18, 0x24, 0x40, 0x4E, 0x42, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'H'
const unsigned char ascii_num1_72[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x44, 0x44, 0x44, 0x7C, 0x44, 0x44, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'I'
const unsigned char ascii_num1_73[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'J'
const unsigned char ascii_num1_74[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x10, 0x10, 0x10, 0x10, 0x10, 0x90, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'K'
const unsigned char ascii_num1_75[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x44, 0x48, 0x50, 0x50, 0x68, 0x44, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'L'
const unsigned char ascii_num1_76[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'M'
const unsigned char ascii_num1_77[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x41, 0x63, 0x63, 0x55, 0x55, 0x55, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'N'
const unsigned char ascii_num1_78[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x44, 0x64, 0x64, 0x54, 0x4C, 0x4C, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'O'
const unsigned char ascii_num1_79[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'P'
const unsigned char ascii_num1_80[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x78, 0x44, 0x44, 0x78, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'Q'
const unsigned char ascii_num1_81[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x42, 0x4C, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'R'
const unsigned char ascii_num1_82[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x78, 0x44, 0x44, 0x78, 0x48, 0x44, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'S'
const unsigned char ascii_num1_83[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x38, 0x44, 0x40, 0x38, 0x04, 0x44, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'T'
const unsigned char ascii_num1_84[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x7C, 0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'U'
const unsigned char ascii_num1_85[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'V'
const unsigned char ascii_num1_86[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x82, 0x44, 0x44, 0x44, 0x28, 0x28, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'W'
const unsigned char ascii_num1_87[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x88, 0x94, 0x55, 0x55, 0x55, 0x55, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'X'
const unsigned char ascii_num1_88[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x44, 0x28, 0x10, 0x10, 0x28, 0x44, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'Y'
const unsigned char ascii_num1_89[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x82, 0x44, 0x28, 0x10, 0x10, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'Z'
const unsigned char ascii_num1_90[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0xFC, 0x0C, 0x18, 0x30, 0x60, 0x80, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 左方括号 '['
const unsigned char ascii_num1_91[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x60, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x60, 0x00, 0x00, 0x00
};
// 反斜杠 '\'
const unsigned char ascii_num1_92[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x80, 0x80, 0x40, 0x40, 0x40, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 右方括号 ']'
const unsigned char ascii_num1_93[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0xC0, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0xC0, 0x00, 0x00, 0x00
};
// 脱字符 '^'
const unsigned char ascii_num1_94[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x20, 0x50, 0x50, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 下划线 '_'
const unsigned char ascii_num1_95[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x00
};
// 反引号 '`'
const unsigned char ascii_num1_96[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x80, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'a'
const unsigned char ascii_num1_97[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x08, 0x38, 0x48, 0x78, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'b'
const unsigned char ascii_num1_98[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x70, 0x48, 0x48, 0x48, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'c'
const unsigned char ascii_num1_99[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x48, 0x40, 0x48, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'd'
const unsigned char ascii_num1_100[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x08, 0x08, 0x38, 0x48, 0x48, 0x48, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'e'
const unsigned char ascii_num1_101[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x48, 0x78, 0x40, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'f'
const unsigned char ascii_num1_102[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x20, 0x40, 0xE0, 0x40, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'g'
const unsigned char ascii_num1_103[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x48, 0x48, 0x48, 0x38, 0x48, 0x30, 0x00, 0x00, 0x00
};
// 'h'
const unsigned char ascii_num1_104[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x70, 0x48, 0x48, 0x48, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'i'
const unsigned char ascii_num1_105[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'j'
const unsigned char ascii_num1_106[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x80, 0x00, 0x00, 0x00
};
// 'k'
const unsigned char ascii_num1_107[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x80, 0x80, 0x90, 0xA0, 0xE0, 0xA0, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'l'
const unsigned char ascii_num1_108[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'm'
const unsigned char ascii_num1_109[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x49, 0x49, 0x49, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'n'
const unsigned char ascii_num1_110[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x48, 0x48, 0x48, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'o'
const unsigned char ascii_num1_111[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x48, 0x48, 0x48, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'p'
const unsigned char ascii_num1_112[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x48, 0x48, 0x48, 0x70, 0x40, 0x40, 0x00, 0x00, 0x00
};
// 'q'
const unsigned char ascii_num1_113[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x48, 0x48, 0x48, 0x38, 0x08, 0x08, 0x00, 0x00, 0x00
};
// 'r'
const unsigned char ascii_num1_114[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x40, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 's'
const unsigned char ascii_num1_115[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x80, 0x60, 0x10, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 't'
const unsigned char ascii_num1_116[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0xE0, 0x40, 0x40, 0x40, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'u'
const unsigned char ascii_num1_117[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x48, 0x48, 0x48, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'v'
const unsigned char ascii_num1_118[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x50, 0x50, 0x50, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'w'
const unsigned char ascii_num1_119[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x92, 0xAA, 0xAA, 0xAA, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'x'
const unsigned char ascii_num1_120[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x50, 0x20, 0x50, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 'y'
const unsigned char ascii_num1_121[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x50, 0x50, 0x50, 0x20, 0x20, 0x40, 0x00, 0x00, 0x00
};
// 'z'
const unsigned char ascii_num1_122[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x10, 0x20, 0x40, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x00
};
// 左花括号 '{'
const unsigned char ascii_num1_123[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x60, 0x40, 0x40, 0x40, 0x80, 0x40, 0x40, 0x40, 0x60, 0x00, 0x00, 0x00
};
// 竖线 '|'
const unsigned char ascii_num1_124[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x40, 0x00, 0x00, 0x00
};
// 右花括号 '}'
const unsigned char ascii_num1_125[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0xC0, 0x40, 0x40, 0x40, 0x20, 0x40, 0x40, 0x40, 0xC0, 0x00, 0x00, 0x00
};
// 波浪号 '~'
const unsigned char ascii_num1_126[] = {
    0x08, 0x0F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE8, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// 指针数组保持不变
const unsigned char * const ascii_num1_table[] = {
   ascii_num1_32, ascii_num1_33, ascii_num1_35, ascii_num1_36, ascii_num1_37,
   ascii_num1_38, ascii_num1_39, ascii_num1_40, ascii_num1_41, ascii_num1_42,
   ascii_num1_43, ascii_num1_44, ascii_num1_45, ascii_num1_46, ascii_num1_47,
   ascii_num1_48, ascii_num1_49, ascii_num1_50, ascii_num1_51, ascii_num1_52,
   ascii_num1_53, ascii_num1_54, ascii_num1_55, ascii_num1_56, ascii_num1_57,
   ascii_num1_58, ascii_num1_59, ascii_num1_60, ascii_num1_61, ascii_num1_62,
   ascii_num1_63, ascii_num1_64, ascii_num1_65, ascii_num1_66, ascii_num1_67,
   ascii_num1_68, ascii_num1_69, ascii_num1_70, ascii_num1_71, ascii_num1_72,
   ascii_num1_73, ascii_num1_74, ascii_num1_75, ascii_num1_76, ascii_num1_77,
   ascii_num1_78, ascii_num1_79, ascii_num1_80, ascii_num1_81, ascii_num1_82,
   ascii_num1_83, ascii_num1_84, ascii_num1_85, ascii_num1_86, ascii_num1_87,
   ascii_num1_88, ascii_num1_89, ascii_num1_90, ascii_num1_91, ascii_num1_92,
   ascii_num1_93, ascii_num1_94, ascii_num1_95, ascii_num1_96, ascii_num1_97,
   ascii_num1_98, ascii_num1_99, ascii_num1_100, ascii_num1_101, ascii_num1_102,
   ascii_num1_103, ascii_num1_104, ascii_num1_105, ascii_num1_106, ascii_num1_107,
   ascii_num1_108, ascii_num1_109, ascii_num1_110, ascii_num1_111, ascii_num1_112,
   ascii_num1_113, ascii_num1_114, ascii_num1_115, ascii_num1_116, ascii_num1_117,
   ascii_num1_118, ascii_num1_119, ascii_num1_120, ascii_num1_121, ascii_num1_122,
   ascii_num1_123, ascii_num1_124, ascii_num1_125, ascii_num1_126
};